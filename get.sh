#!/bin/bash

# Enhanced Server Analysis and Dockerfile Generator
# Gera uma análise completa do servidor para replicação fiel via Docker

# Solicitar nome do container
echo "=== Enhanced Server Analysis and Dockerfile Generator ==="
echo ""
read -p "Digite o nome do container: " CONTAINER_NAME

# Validar nome do container
if [[ -z "$CONTAINER_NAME" ]]; then
    echo "Erro: Nome do container não pode estar vazio!"
    exit 1
fi

# Criar diretório para o container
CONTAINER_DIR="$CONTAINER_NAME"
if [[ -d "$CONTAINER_DIR" ]]; then
    echo "Diretório '$CONTAINER_DIR' já existe. Deseja continuar? (y/N)"
    read -p "> " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "Operação cancelada."
        exit 1
    fi
fi

mkdir -p "$CONTAINER_DIR"
cd "$CONTAINER_DIR"

echo "Criando arquivos no diretório: $(pwd)"
echo ""

# Define os nomes dos arquivos de saída
DOCKERFILE="output.Dockerfile"
INFOFILE="docker_info.log"
PACKAGES_FILE="detected_packages.txt"
SERVICES_FILE="detected_services.txt"
CRONTAB_FILE="detected_crontabs.txt"
CONFIGS_FILE="detected_configs.txt"
CUSTOM_PACKAGES_FILE="custom_packages.txt"
APP_DIRS_FILE="application_directories.txt"

# Limpar arquivos anteriores
> "$INFOFILE"
> "$PACKAGES_FILE"
> "$SERVICES_FILE"
> "$CRONTAB_FILE"
> "$CONFIGS_FILE"
> "$CUSTOM_PACKAGES_FILE"
> "$APP_DIRS_FILE"

echo "=== Iniciando análise completa do servidor ===" | tee -a "$INFOFILE"
echo "Container: $CONTAINER_NAME" | tee -a "$INFOFILE"
echo "Data/Hora: $(date)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Sistema Operacional
# -----------------------------
echo "=== Detectando Sistema Operacional ===" | tee -a "$INFOFILE"

OS_ID=""
OS_VER=""
if [ -f /etc/os-release ]; then
  OS_ID=$(awk -F= '/^ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release || echo "")
  OS_VER=$(awk -F= '/^VERSION_ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release || echo "")
  echo "Sistema: $OS_ID $OS_VER" | tee -a "$INFOFILE"
fi

# Determinar base image
BASE_IMAGE="centos:7"
if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]] && [ -n "$OS_VER" ]; then
  if [[ "$OS_VER" == "8" ]]; then
    BASE_IMAGE="rockylinux:8"
  elif [[ "$OS_VER" == "9" ]]; then
    BASE_IMAGE="rockylinux:9"
  else
    BASE_IMAGE="centos:${OS_VER}"
  fi
elif [[ "$OS_ID" =~ fedora ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="fedora:${OS_VER}"
elif [[ "$OS_ID" =~ ubuntu ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="ubuntu:${OS_VER}"
elif [[ "$OS_ID" =~ debian ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="debian:${OS_VER}"
fi

echo "Base image selecionada: $BASE_IMAGE" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Lista de Pacotes Mínimos (Base System)
# -----------------------------
BASE_PACKAGES=(
  "bash" "coreutils" "util-linux" "systemd" "glibc" "yum" "rpm" 
  "filesystem" "setup" "basesystem" "tzdata" "rootfiles" "vim-minimal"
  "procps-ng" "kmod" "systemd-libs" "dbus" "dbus-libs" "kernel"
  "initscripts" "chkconfig" "service" "which" "findutils" "grep"
  "sed" "gawk" "tar" "gzip" "bzip2" "xz" "less" "shadow-utils"
  "passwd" "cronie" "logrotate" "rsyslog" "ca-certificates"
  "curl" "wget" "openssh" "openssh-server" "openssh-clients"
  "net-tools" "iproute" "iputils" "bind-utils" "telnet"
  "centos-release" "redhat-release" "system-release"
)

# -----------------------------
# Detectar Pacotes Instalados
# -----------------------------
echo "=== Coletando lista de pacotes instalados ===" | tee -a "$INFOFILE"

if command -v rpm >/dev/null 2>&1; then
  echo "Sistema baseado em RPM detectado" | tee -a "$INFOFILE"
  
  # Todos os pacotes instalados
  rpm -qa --qf "%{NAME}\n" | sort > "$PACKAGES_FILE"
  TOTAL_PACKAGES=$(wc -l < "$PACKAGES_FILE")
  echo "Total de pacotes instalados: $TOTAL_PACKAGES" | tee -a "$INFOFILE"
  
  # Detectar pacotes personalizados (não são do sistema base)
  echo "=== Detectando pacotes personalizados ===" | tee -a "$INFOFILE"
  
  while read -r pkg; do
    # Verificar se não é um pacote base
    is_base=false
    for base_pkg in "${BASE_PACKAGES[@]}"; do
      if [[ "$pkg" == "$base_pkg"* ]]; then
        is_base=true
        break
      fi
    done
    
    if [ "$is_base" = false ]; then
      # Verificar se é um pacote interessante (serviços, desenvolvimento, etc.)
      if [[ "$pkg" =~ ^(httpd|nginx|apache|mysql|mariadb|postgresql|php|python|nodejs|java|git|docker|kubernetes|redis|memcached|elasticsearch|mongodb|rabbitmq|tomcat|jenkins|ansible|terraform|vim|emacs|nano|htop|tmux|screen|zsh|fish|certbot|letsencrypt|firewalld|iptables|fail2ban|ntp|chrony|samba|nfs|ftp|vsftpd|postfix|dovecot|bind|named|squid|haproxy|keepalived|pacemaker|corosync|rsync|backup|borg|rclone|awscli|azure-cli|google-cloud|kubectl|helm|prometheus|grafana|zabbix|nagios|icinga|collectd|telegraf|influxdb|gcc|make|cmake|autoconf|automake|libtool|pkgconfig|rpm-build|mock|createrepo|yum-utils|epel-release|remi-release|ius-release|webtatic-release) ]]; then
        echo "$pkg" >> "$CUSTOM_PACKAGES_FILE"
      fi
    fi
  done < "$PACKAGES_FILE"
  
  # Detectar repositórios adicionais
  echo "=== Repositórios habilitados ===" | tee -a "$INFOFILE"
  if command -v yum >/dev/null 2>&1; then
    yum repolist enabled 2>/dev/null | grep -v "^repo" | grep -v "^Loaded" | grep -v "^repolist" | tee -a "$INFOFILE"
  fi

elif command -v dpkg >/dev/null 2>&1; then
  echo "Sistema baseado em DEB detectado" | tee -a "$INFOFILE"
  dpkg-query -W -f='${Package}\n' | sort > "$PACKAGES_FILE"
  
  # Para sistemas Debian/Ubuntu, detectar pacotes não essenciais
  while read -r pkg; do
    if [[ "$pkg" =~ ^(apache2|nginx|mysql|mariadb|postgresql|php|python|nodejs|java|git|docker|redis|memcached|elasticsearch|mongodb|rabbitmq|tomcat|jenkins|certbot|ufw|fail2ban|ntp|samba|nfs|vsftpd|postfix|dovecot|bind9|squid|haproxy|rsync|awscli|kubectl) ]]; then
      echo "$pkg" >> "$CUSTOM_PACKAGES_FILE"
    fi
  done < "$PACKAGES_FILE"
fi

CUSTOM_PACKAGES_COUNT=$(wc -l < "$CUSTOM_PACKAGES_FILE" 2>/dev/null || echo "0")
echo "Pacotes personalizados detectados: $CUSTOM_PACKAGES_COUNT" | tee -a "$INFOFILE"

if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
  echo "Principais pacotes personalizados:" | tee -a "$INFOFILE"
  head -20 "$CUSTOM_PACKAGES_FILE" | while read -r pkg; do
    echo "  - $pkg" | tee -a "$INFOFILE"
  done
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Serviços Habilitados
# -----------------------------
echo "=== Coletando serviços habilitados ===" | tee -a "$INFOFILE"

if command -v systemctl >/dev/null 2>&1; then
  systemctl list-unit-files --type=service --state=enabled --no-pager --no-legend | awk '{print $1}' > "$SERVICES_FILE"
  echo "Serviços habilitados:" | tee -a "$INFOFILE"
  head -10 "$SERVICES_FILE" | while read -r service; do
    echo "  - $service" | tee -a "$INFOFILE"
  done
elif command -v chkconfig >/dev/null 2>&1; then
  chkconfig --list 2>/dev/null | grep ":on" | awk '{print $1}' > "$SERVICES_FILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Descobrir Diretórios de Aplicações
# -----------------------------
echo "=== Descobrindo diretórios de aplicações ===" | tee -a "$INFOFILE"

# Função para verificar se um diretório contém aplicações
is_app_directory() {
    local dir="$1"
    local file_count=$(find "$dir" -maxdepth 2 -type f \( -name "*.php" -o -name "*.py" -o -name "*.js" -o -name "*.jar" -o -name "*.war" -o -name "*.rb" -o -name "*.pl" -o -name "*.go" -o -name "*.sh" -o -name "index.*" -o -name "app.py" -o -name "main.*" -o -name "server.*" -o -name "package.json" -o -name "composer.json" -o -name "requirements.txt" -o -name "Gemfile" -o -name "pom.xml" -o -name "build.gradle" -o -name "Dockerfile" -o -name "docker-compose.yml" \) 2>/dev/null | wc -l)

    if [ "$file_count" -gt 0 ]; then
        return 0  # É um diretório de aplicação
    fi

    # Verificar se tem estrutura típica de aplicação web
    if [ -d "$dir/public" ] || [ -d "$dir/static" ] || [ -d "$dir/assets" ] || [ -d "$dir/templates" ] || [ -d "$dir/views" ] || [ -d "$dir/src" ] || [ -d "$dir/lib" ] || [ -d "$dir/bin" ]; then
        return 0
    fi

    return 1  # Não é um diretório de aplicação
}

# Diretórios base para buscar aplicações
BASE_SEARCH_DIRS=(
    "/var/www"
    "/srv"
    "/opt"
    "/usr/local"
    "/home"
    "/var/lib"
    "/usr/share"
)

# Buscar diretórios de aplicações
echo "Buscando diretórios de aplicações..." | tee -a "$INFOFILE"
for base_dir in "${BASE_SEARCH_DIRS[@]}"; do
    if [ -d "$base_dir" ]; then
        echo "Analisando $base_dir..." | tee -a "$INFOFILE"

        # Buscar até 3 níveis de profundidade, evitando diretórios muito genéricos
        find "$base_dir" -maxdepth 3 -type d \
            ! -path "*/.*" \
            ! -path "*/tmp*" \
            ! -path "*/temp*" \
            ! -path "*/cache*" \
            ! -path "*/log*" \
            ! -path "*/logs*" \
            ! -path "*/var*" \
            ! -path "*/proc*" \
            ! -path "*/sys*" \
            ! -path "*/dev*" \
            ! -path "*/run*" \
            ! -path "*/media*" \
            ! -path "*/mnt*" \
            ! -path "*/boot*" \
            2>/dev/null | while read -r dir; do

            if is_app_directory "$dir"; then
                echo "$dir" >> "$APP_DIRS_FILE"
                echo "  Encontrado: $dir" | tee -a "$INFOFILE"
            fi
        done
    fi
done

# Remover duplicatas e ordenar
if [ -s "$APP_DIRS_FILE" ]; then
    sort -u "$APP_DIRS_FILE" -o "$APP_DIRS_FILE"
    APP_DIRS_COUNT=$(wc -l < "$APP_DIRS_FILE")
    echo "Total de diretórios de aplicação encontrados: $APP_DIRS_COUNT" | tee -a "$INFOFILE"
else
    echo "Nenhum diretório de aplicação encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Coletar Crontab (apenas root)
# -----------------------------
echo "=== Coletando crontab do root ===" | tee -a "$INFOFILE"

# Apenas crontab do root, preservando comentários
echo "# ROOT CRONTAB - Coletado em $(date)" > "$CRONTAB_FILE"
echo "# Servidor: $(hostname)" >> "$CRONTAB_FILE"
echo "" >> "$CRONTAB_FILE"

# Tentar diferentes métodos para obter a crontab do root
if crontab -l -u root 2>/dev/null >> "$CRONTAB_FILE"; then
    echo "Crontab do root coletada via 'crontab -l'" | tee -a "$INFOFILE"
elif [ -f /var/spool/cron/root ]; then
    cat /var/spool/cron/root >> "$CRONTAB_FILE"
    echo "Crontab do root coletada de /var/spool/cron/root" | tee -a "$INFOFILE"
elif [ -f /var/spool/cron/crontabs/root ]; then
    cat /var/spool/cron/crontabs/root >> "$CRONTAB_FILE"
    echo "Crontab do root coletada de /var/spool/cron/crontabs/root" | tee -a "$INFOFILE"
else
    echo "# Nenhuma crontab encontrada para o usuário root" >> "$CRONTAB_FILE"
    echo "Nenhuma crontab encontrada para o usuário root" | tee -a "$INFOFILE"
fi

echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Configurações Importantes (Expandido)
# -----------------------------
echo "=== Detectando arquivos de configuração importantes ===" | tee -a "$INFOFILE"

# Função para buscar arquivos de configuração em diretórios
find_config_files() {
    local search_dir="$1"
    local pattern="$2"

    if [ -d "$search_dir" ]; then
        find "$search_dir" -name "$pattern" -type f 2>/dev/null
    fi
}

# Buscar configurações do Apache (incluindo virtualhosts)
echo "Buscando configurações do Apache..." | tee -a "$INFOFILE"
for apache_dir in /etc/httpd /etc/apache2; do
    if [ -d "$apache_dir" ]; then
        # Configuração principal
        find_config_files "$apache_dir" "*.conf" >> "$CONFIGS_FILE"

        # Virtualhosts
        find_config_files "$apache_dir/sites-available" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/sites-enabled" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/conf.d" "*.conf" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/conf-available" "*.conf" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/conf-enabled" "*.conf" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/vhosts.d" "*.conf" >> "$CONFIGS_FILE"
    fi
done

# Buscar configurações do Nginx (incluindo sites)
echo "Buscando configurações do Nginx..." | tee -a "$INFOFILE"
for nginx_dir in /etc/nginx; do
    if [ -d "$nginx_dir" ]; then
        find_config_files "$nginx_dir" "*.conf" >> "$CONFIGS_FILE"
        find_config_files "$nginx_dir/sites-available" "*" >> "$CONFIGS_FILE"
        find_config_files "$nginx_dir/sites-enabled" "*" >> "$CONFIGS_FILE"
        find_config_files "$nginx_dir/conf.d" "*.conf" >> "$CONFIGS_FILE"
    fi
done

# Configurações de PHP
echo "Buscando configurações do PHP..." | tee -a "$INFOFILE"
for php_dir in /etc/php* /etc/php /usr/local/etc/php*; do
    if [ -d "$php_dir" ]; then
        find_config_files "$php_dir" "php.ini" >> "$CONFIGS_FILE"
        find_config_files "$php_dir" "*.ini" >> "$CONFIGS_FILE"
        find_config_files "$php_dir/conf.d" "*.ini" >> "$CONFIGS_FILE"
        find_config_files "$php_dir/fpm" "*.conf" >> "$CONFIGS_FILE"
    fi
done

# Configurações de banco de dados
echo "Buscando configurações de bancos de dados..." | tee -a "$INFOFILE"
IMPORTANT_CONFIGS=(
    "/etc/my.cnf"
    "/etc/mysql/my.cnf"
    "/etc/mysql/mysql.conf.d/*.cnf"
    "/etc/mysql/conf.d/*.cnf"
    "/etc/postgresql/*/main/postgresql.conf"
    "/etc/postgresql/*/main/pg_hba.conf"
    "/var/lib/pgsql/data/postgresql.conf"
    "/var/lib/pgsql/data/pg_hba.conf"
    "/etc/redis.conf"
    "/etc/redis/redis.conf"
    "/etc/mongodb.conf"
    "/etc/mongod.conf"
)

# Outras configurações importantes
IMPORTANT_CONFIGS+=(
    "/etc/postfix/main.cf"
    "/etc/postfix/master.cf"
    "/etc/dovecot/dovecot.conf"
    "/etc/dovecot/conf.d/*.conf"
    "/etc/ssh/sshd_config"
    "/etc/sudoers"
    "/etc/sudoers.d/*"
    "/etc/hosts"
    "/etc/resolv.conf"
    "/etc/fstab"
    "/etc/exports"
    "/etc/samba/smb.conf"
    "/etc/bind/named.conf"
    "/etc/bind/named.conf.local"
    "/etc/bind/named.conf.options"
    "/etc/firewalld/zones/*.xml"
    "/etc/iptables/rules.v4"
    "/etc/iptables/rules.v6"
    "/etc/fail2ban/jail.local"
    "/etc/fail2ban/jail.d/*.conf"
    "/etc/logrotate.d/*"
    "/etc/systemd/system/*.service"
    "/etc/cron.d/*"
    "/etc/environment"
    "/etc/profile.d/*.sh"
)

# Buscar todos os arquivos de configuração
for config in "${IMPORTANT_CONFIGS[@]}"; do
    for file in $config; do
        if [ -f "$file" ]; then
            echo "$file" >> "$CONFIGS_FILE"
        fi
    done
done

# Buscar configurações em diretórios de aplicações descobertos
if [ -s "$APP_DIRS_FILE" ]; then
    echo "Buscando configurações nos diretórios de aplicações..." | tee -a "$INFOFILE"
    while read -r app_dir; do
        if [ -d "$app_dir" ]; then
            # Buscar arquivos de configuração comuns
            find_config_files "$app_dir" "*.conf" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "*.ini" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "*.cfg" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "*.yaml" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "*.yml" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "*.json" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" ".env*" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "config.*" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "settings.*" >> "$CONFIGS_FILE"
        fi
    done < "$APP_DIRS_FILE"
fi

# Remover duplicatas e arquivos muito grandes (logs, etc.)
if [ -s "$CONFIGS_FILE" ]; then
    # Filtrar arquivos muito grandes (> 10MB) e logs
    temp_configs=$(mktemp)
    while read -r config_file; do
        if [ -f "$config_file" ]; then
            file_size=$(stat -f%z "$config_file" 2>/dev/null || stat -c%s "$config_file" 2>/dev/null || echo "0")
            if [ "$file_size" -lt 10485760 ] && [[ ! "$config_file" =~ \.(log|LOG)$ ]]; then
                echo "$config_file" >> "$temp_configs"
            fi
        fi
    done < "$CONFIGS_FILE"

    sort -u "$temp_configs" -o "$CONFIGS_FILE"
    rm -f "$temp_configs"

    CONFIG_COUNT=$(wc -l < "$CONFIGS_FILE")
    echo "Arquivos de configuração encontrados: $CONFIG_COUNT" | tee -a "$INFOFILE"

    echo "Principais arquivos de configuração:" | tee -a "$INFOFILE"
    head -20 "$CONFIGS_FILE" | while read -r config; do
        echo "  - $config" | tee -a "$INFOFILE"
    done
else
    echo "Nenhum arquivo de configuração especial encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Usuários do Sistema
# -----------------------------
echo "=== Coletando usuários do sistema ===" | tee -a "$INFOFILE"
USERFILE="detected_users.txt"

awk -F: '$3 >= 1000 && $3 < 65534 {print $1":"$3":"$6":"$7}' /etc/passwd > "$USERFILE"
if [ -s "$USERFILE" ]; then
  echo "Usuários personalizados encontrados:" | tee -a "$INFOFILE"
  cat "$USERFILE" | while IFS=: read -r uname uid home shell; do
    echo "  - $uname (UID: $uid, Home: $home, Shell: $shell)" | tee -a "$INFOFILE"
  done
else
  echo "Nenhum usuário personalizado encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Preparar lista de diretórios para backup
# -----------------------------
echo "=== Preparando lista de diretórios para backup ===" | tee -a "$INFOFILE"

# Combinar diretórios de aplicação descobertos com diretórios essenciais
ESSENTIAL_DIRS=("/etc" "/var/lib/mysql" "/var/lib/postgresql")
DEPLOY_DIRS=()

# Adicionar diretórios essenciais
for dir in "${ESSENTIAL_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        DEPLOY_DIRS+=("$dir")
    fi
done

# Adicionar diretórios de aplicação descobertos
if [ -s "$APP_DIRS_FILE" ]; then
    while read -r app_dir; do
        if [ -d "$app_dir" ]; then
            DEPLOY_DIRS+=("$app_dir")
        fi
    done < "$APP_DIRS_FILE"
fi

echo "Diretórios selecionados para backup:" | tee -a "$INFOFILE"
for dir in "${DEPLOY_DIRS[@]}"; do
    echo "  - $dir" | tee -a "$INFOFILE"
done
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Portas em Uso
# -----------------------------
echo "=== Detectando portas em uso ===" | tee -a "$INFOFILE"

if command -v ss >/dev/null 2>&1; then
  PORTS=$(ss -tuln | awk 'NR>1 {gsub(/.*:/,"",$5); print $5}' | sort -n -u | tr '\n' ' ')
else
  PORTS=$(netstat -tuln 2>/dev/null | awk 'NR>2 {gsub(/.*:/,"",$4); print $4}' | sort -n -u | tr '\n' ' ')
fi

echo "Portas detectadas: $PORTS" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Gerar Dockerfile Avançado
# -----------------------------
echo "=== Gerando Dockerfile avançado ===" | tee -a "$INFOFILE"

{
  echo "# Generated by Enhanced Server Analysis Script"
  echo "# Date: $(date)"
  echo "# Base System: $OS_ID $OS_VER"
  echo "# Custom Packages: $CUSTOM_PACKAGES_COUNT"
  echo ""
  echo "FROM ${BASE_IMAGE}"
  echo ""
  echo "LABEL maintainer=\"server-replica@local\""
  echo "LABEL description=\"Replica of production server $(hostname)\""
  echo "LABEL created=\"$(date -I)\""
  echo ""
  echo "ENV container docker"
  echo "ENV DEBIAN_FRONTEND=noninteractive"
  echo ""

  # Instalar repositórios adicionais primeiro
  if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]]; then
    echo "# Instalar repositórios adicionais"
    echo "RUN yum -y update && yum -y install epel-release yum-utils"
    
    # Detectar se tem repositórios Remi, IUS, etc.
    if rpm -qa | grep -q remi-release; then
      echo "RUN yum -y install https://rpms.remirepo.net/enterprise/remi-release-\$(rpm -E %rhel).rpm"
    fi
    if rpm -qa | grep -q ius-release; then
      echo "RUN yum -y install https://repo.ius.io/ius-release-el\$(rpm -E %rhel).rpm"
    fi
    echo ""
  fi

  # Instalar pacotes personalizados
  if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
    echo "# Instalar pacotes personalizados detectados"
    
    if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]]; then
      # Agrupar pacotes em chunks para evitar linhas muito longas
      CHUNK_SIZE=10
      i=0
      while IFS= read -r pkg && [ -n "$pkg" ]; do
        if [ $((i % CHUNK_SIZE)) -eq 0 ]; then
          if [ $i -gt 0 ]; then
            echo " && yum clean all"
          fi
          echo -n "RUN yum -y install $pkg"
        else
          echo -n " $pkg"
        fi
        i=$((i+1))
      done < "$CUSTOM_PACKAGES_FILE"
      
      if [ $i -gt 0 ]; then
        echo " && yum clean all"
      fi
      
    elif [[ "$OS_ID" =~ ubuntu|debian ]]; then
      echo "RUN apt-get update && apt-get install -y \\"
      while IFS= read -r pkg && [ -n "$pkg" ]; do
        echo "    $pkg \\"
      done < "$CUSTOM_PACKAGES_FILE"
      echo " && apt-get clean && rm -rf /var/lib/apt/lists/*"
    fi
    echo ""
  fi

  # Criar usuários
  if [ -s "$USERFILE" ]; then
    echo "# Criar usuários detectados"
    while IFS=: read -r uname uid home shell; do
      [ -z "$uname" ] && continue
      echo "RUN groupadd -g ${uid} ${uname} 2>/dev/null || true"
      echo "RUN useradd -m -u ${uid} -g ${uid} -s ${shell} -d ${home} ${uname} 2>/dev/null || true"
    done < "$USERFILE"
    echo ""
  fi

  # Configurar crontab do root
  if [ -s "$CRONTAB_FILE" ]; then
    echo "# Configurar crontab do root"
    echo "RUN yum -y install cronie || apt-get update && apt-get install -y cron"
    echo "COPY crontabs.txt /tmp/crontabs.txt"
    echo "RUN crontab -u root /tmp/crontabs.txt 2>/dev/null || true"
    echo ""
  fi

  # Copiar diretórios importantes
  echo "# Copiar diretórios de aplicação e configuração"
  for d in "${DEPLOY_DIRS[@]}"; do
    for realdir in $d; do
      if [ -d "$realdir" ]; then
        # Verificar se o diretório tem conteúdo relevante
        if [ "$(find "$realdir" -type f | wc -l)" -gt 0 ]; then
          echo "COPY ./backup$(echo $realdir) $realdir/"
        fi
      fi
    done
  done
  echo ""

  # Configurar serviços
  if [ -s "$SERVICES_FILE" ]; then
    echo "# Habilitar serviços detectados"
    while read -r service; do
      if [[ "$service" != *"@"* ]] && [[ "$service" != "getty"* ]]; then
        echo "RUN systemctl enable $service 2>/dev/null || true"
      fi
    done < "$SERVICES_FILE"
    echo ""
  fi

  # Expor portas
  if [ -n "$PORTS" ]; then
    echo "# Expor portas detectadas"
    for p in $PORTS; do
      pn=$(echo "$p" | sed 's/[^0-9].*$//')
      if [ -n "$pn" ] && [ "$pn" -gt 0 ] && [ "$pn" -lt 65536 ]; then
        echo "EXPOSE ${pn}"
      fi
    done
    echo ""
  fi

  # Volumes para dados persistentes
  echo "# Volumes para dados persistentes"
  echo "VOLUME [\"/var/log\", \"/var/lib/mysql\", \"/var/www\", \"/etc\"]"
  echo ""

  # Healthcheck
  echo "# Healthcheck básico"
  echo "HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\"
  echo "  CMD curl -f http://localhost/ || exit 1"
  echo ""

  # Comando de inicialização
  echo "# Script de inicialização"
  echo "COPY docker-entrypoint.sh /usr/local/bin/"
  echo "RUN chmod +x /usr/local/bin/docker-entrypoint.sh"
  echo ""
  echo "ENTRYPOINT [\"/usr/local/bin/docker-entrypoint.sh\"]"
  echo "CMD [\"/sbin/init\"]"
  
} > "$DOCKERFILE"

# -----------------------------
# Gerar Script de Entrypoint
# -----------------------------
cat > "docker-entrypoint.sh" << 'EOF'
#!/bin/bash

# Docker Entrypoint Script
# Inicializa serviços e configurações

# Iniciar cron
if command -v crond >/dev/null 2>&1; then
    crond
elif command -v cron >/dev/null 2>&1; then
    cron
fi

# Iniciar outros serviços essenciais
if command -v systemctl >/dev/null 2>&1; then
    systemctl start rsyslog 2>/dev/null || true
fi

# Executar comando passado como argumento
exec "$@"
EOF

chmod +x "docker-entrypoint.sh"

# -----------------------------
# Gerar Scripts de Backup
# -----------------------------
# Gerar script de backup dinâmico baseado nos diretórios descobertos
cat > "backup_server.sh" << EOF
#!/bin/bash

# Script para fazer backup dos diretórios importantes
# Execute este script no servidor original antes de usar o Dockerfile
# Gerado automaticamente em $(date)

BACKUP_DIR="./backup"
mkdir -p "\$BACKUP_DIR"

echo "=== Iniciando backup dos diretórios descobertos ==="
echo "Data/Hora: \$(date)"
echo ""

# Diretórios descobertos automaticamente:
EOF

# Adicionar os diretórios descobertos ao script
for dir in "${DEPLOY_DIRS[@]}"; do
    cat >> "backup_server.sh" << EOF
if [ -d "$dir" ]; then
    echo "Fazendo backup de $dir..."
    mkdir -p "\$BACKUP_DIR$dir"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \\
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \\
          --exclude='sys' --exclude='dev' --exclude='run' \\
          "$dir/" "\$BACKUP_DIR$dir/" 2>/dev/null || true
    echo "  Backup de $dir concluído"
fi

EOF
done

cat >> "backup_server.sh" << 'EOF'
echo ""
echo "=== Backup completo ==="
echo "Diretório de backup: $BACKUP_DIR"
echo "Tamanho total: $(du -sh $BACKUP_DIR 2>/dev/null | cut -f1)"
echo "Arquivos copiados: $(find $BACKUP_DIR -type f | wc -l)"
echo ""
echo "Para usar com Docker:"
echo "1. docker build -t $CONTAINER_NAME ."
echo "2. docker run -d --name $CONTAINER_NAME $CONTAINER_NAME"
EOF

chmod +x "backup_server.sh"

# -----------------------------
# Resumo Final
# -----------------------------
echo "================================" | tee -a "$INFOFILE"
echo "ANÁLISE COMPLETA FINALIZADA" | tee -a "$INFOFILE"
echo "================================" | tee -a "$INFOFILE"
echo "Container: $CONTAINER_NAME" | tee -a "$INFOFILE"
echo "Diretório: $(pwd)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"
echo "Arquivos gerados:" | tee -a "$INFOFILE"
echo "  - $DOCKERFILE (Dockerfile principal)" | tee -a "$INFOFILE"
echo "  - docker-entrypoint.sh (Script de inicialização)" | tee -a "$INFOFILE"
echo "  - backup_server.sh (Script de backup dinâmico)" | tee -a "$INFOFILE"
echo "  - $INFOFILE (Log da análise)" | tee -a "$INFOFILE"
echo "  - $PACKAGES_FILE (Lista completa de pacotes)" | tee -a "$INFOFILE"
echo "  - $CUSTOM_PACKAGES_FILE (Pacotes personalizados)" | tee -a "$INFOFILE"
echo "  - $SERVICES_FILE (Serviços habilitados)" | tee -a "$INFOFILE"
echo "  - $CRONTAB_FILE (Crontab do root)" | tee -a "$INFOFILE"
echo "  - $CONFIGS_FILE (Arquivos de configuração)" | tee -a "$INFOFILE"
echo "  - $APP_DIRS_FILE (Diretórios de aplicação)" | tee -a "$INFOFILE"
echo "  - $USERFILE (Usuários personalizados)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# Estatísticas finais
if [ -s "$APP_DIRS_FILE" ]; then
    APP_COUNT=$(wc -l < "$APP_DIRS_FILE")
    echo "Estatísticas:" | tee -a "$INFOFILE"
    echo "  - Diretórios de aplicação: $APP_COUNT" | tee -a "$INFOFILE"
fi

if [ -s "$CONFIGS_FILE" ]; then
    CONFIG_COUNT=$(wc -l < "$CONFIGS_FILE")
    echo "  - Arquivos de configuração: $CONFIG_COUNT" | tee -a "$INFOFILE"
fi

if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
    PACKAGE_COUNT=$(wc -l < "$CUSTOM_PACKAGES_FILE")
    echo "  - Pacotes personalizados: $PACKAGE_COUNT" | tee -a "$INFOFILE"
fi

echo "" | tee -a "$INFOFILE"
echo "PRÓXIMOS PASSOS:" | tee -a "$INFOFILE"
echo "1. Execute './backup_server.sh' para fazer backup dos dados" | tee -a "$INFOFILE"
echo "2. Construa a imagem: docker build -t $CONTAINER_NAME ." | tee -a "$INFOFILE"
echo "3. Execute o container: docker run -d --name $CONTAINER_NAME $CONTAINER_NAME" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# Copiar crontabs para uso no Dockerfile
if [ -s "$CRONTAB_FILE" ]; then
    cp "$CRONTAB_FILE" crontabs.txt
    echo "Crontab copiada para crontabs.txt" | tee -a "$INFOFILE"
fi

echo "Script finalizado com sucesso!" | tee -a "$INFOFILE"
echo ""
echo "Todos os arquivos foram criados no diretório: $(pwd)"
echo "Execute 'cd $CONTAINER_NAME' para acessar os arquivos gerados."