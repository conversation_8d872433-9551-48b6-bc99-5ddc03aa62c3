#!/bin/bash

# Enhanced Server Analysis and Dockerfile Generator
# Gera uma análise completa do servidor para replicação fiel via Docker

# Define os nomes dos arquivos de saída
DOCKERFILE="output.Dockerfile"
INFOFILE="docker_info.log"
PACKAGES_FILE="detected_packages.txt"
SERVICES_FILE="detected_services.txt"
CRONTAB_FILE="detected_crontabs.txt"
CONFIGS_FILE="detected_configs.txt"
CUSTOM_PACKAGES_FILE="custom_packages.txt"

# Limpar arquivos anteriores
> "$INFOFILE"
> "$PACKAGES_FILE"
> "$SERVICES_FILE"
> "$CRONTAB_FILE"
> "$CONFIGS_FILE"
> "$CUSTOM_PACKAGES_FILE"

echo "=== Iniciando análise completa do servidor ===" | tee -a "$INFOFILE"
echo "Data/Hora: $(date)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Sistema Operacional
# -----------------------------
echo "=== Detectando Sistema Operacional ===" | tee -a "$INFOFILE"

OS_ID=""
OS_VER=""
if [ -f /etc/os-release ]; then
  OS_ID=$(awk -F= '/^ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release || echo "")
  OS_VER=$(awk -F= '/^VERSION_ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release || echo "")
  echo "Sistema: $OS_ID $OS_VER" | tee -a "$INFOFILE"
fi

# Determinar base image
BASE_IMAGE="centos:7"
if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]] && [ -n "$OS_VER" ]; then
  if [[ "$OS_VER" == "8" ]]; then
    BASE_IMAGE="rockylinux:8"
  elif [[ "$OS_VER" == "9" ]]; then
    BASE_IMAGE="rockylinux:9"
  else
    BASE_IMAGE="centos:${OS_VER}"
  fi
elif [[ "$OS_ID" =~ fedora ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="fedora:${OS_VER}"
elif [[ "$OS_ID" =~ ubuntu ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="ubuntu:${OS_VER}"
elif [[ "$OS_ID" =~ debian ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="debian:${OS_VER}"
fi

echo "Base image selecionada: $BASE_IMAGE" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Lista de Pacotes Mínimos (Base System)
# -----------------------------
BASE_PACKAGES=(
  "bash" "coreutils" "util-linux" "systemd" "glibc" "yum" "rpm" 
  "filesystem" "setup" "basesystem" "tzdata" "rootfiles" "vim-minimal"
  "procps-ng" "kmod" "systemd-libs" "dbus" "dbus-libs" "kernel"
  "initscripts" "chkconfig" "service" "which" "findutils" "grep"
  "sed" "gawk" "tar" "gzip" "bzip2" "xz" "less" "shadow-utils"
  "passwd" "cronie" "logrotate" "rsyslog" "ca-certificates"
  "curl" "wget" "openssh" "openssh-server" "openssh-clients"
  "net-tools" "iproute" "iputils" "bind-utils" "telnet"
  "centos-release" "redhat-release" "system-release"
)

# -----------------------------
# Detectar Pacotes Instalados
# -----------------------------
echo "=== Coletando lista de pacotes instalados ===" | tee -a "$INFOFILE"

if command -v rpm >/dev/null 2>&1; then
  echo "Sistema baseado em RPM detectado" | tee -a "$INFOFILE"
  
  # Todos os pacotes instalados
  rpm -qa --qf "%{NAME}\n" | sort > "$PACKAGES_FILE"
  TOTAL_PACKAGES=$(wc -l < "$PACKAGES_FILE")
  echo "Total de pacotes instalados: $TOTAL_PACKAGES" | tee -a "$INFOFILE"
  
  # Detectar pacotes personalizados (não são do sistema base)
  echo "=== Detectando pacotes personalizados ===" | tee -a "$INFOFILE"
  
  while read -r pkg; do
    # Verificar se não é um pacote base
    is_base=false
    for base_pkg in "${BASE_PACKAGES[@]}"; do
      if [[ "$pkg" == "$base_pkg"* ]]; then
        is_base=true
        break
      fi
    done
    
    if [ "$is_base" = false ]; then
      # Verificar se é um pacote interessante (serviços, desenvolvimento, etc.)
      if [[ "$pkg" =~ ^(httpd|nginx|apache|mysql|mariadb|postgresql|php|python|nodejs|java|git|docker|kubernetes|redis|memcached|elasticsearch|mongodb|rabbitmq|tomcat|jenkins|ansible|terraform|vim|emacs|nano|htop|tmux|screen|zsh|fish|certbot|letsencrypt|firewalld|iptables|fail2ban|ntp|chrony|samba|nfs|ftp|vsftpd|postfix|dovecot|bind|named|squid|haproxy|keepalived|pacemaker|corosync|rsync|backup|borg|rclone|awscli|azure-cli|google-cloud|kubectl|helm|prometheus|grafana|zabbix|nagios|icinga|collectd|telegraf|influxdb|gcc|make|cmake|autoconf|automake|libtool|pkgconfig|rpm-build|mock|createrepo|yum-utils|epel-release|remi-release|ius-release|webtatic-release) ]]; then
        echo "$pkg" >> "$CUSTOM_PACKAGES_FILE"
      fi
    fi
  done < "$PACKAGES_FILE"
  
  # Detectar repositórios adicionais
  echo "=== Repositórios habilitados ===" | tee -a "$INFOFILE"
  if command -v yum >/dev/null 2>&1; then
    yum repolist enabled 2>/dev/null | grep -v "^repo" | grep -v "^Loaded" | grep -v "^repolist" | tee -a "$INFOFILE"
  fi

elif command -v dpkg >/dev/null 2>&1; then
  echo "Sistema baseado em DEB detectado" | tee -a "$INFOFILE"
  dpkg-query -W -f='${Package}\n' | sort > "$PACKAGES_FILE"
  
  # Para sistemas Debian/Ubuntu, detectar pacotes não essenciais
  while read -r pkg; do
    if [[ "$pkg" =~ ^(apache2|nginx|mysql|mariadb|postgresql|php|python|nodejs|java|git|docker|redis|memcached|elasticsearch|mongodb|rabbitmq|tomcat|jenkins|certbot|ufw|fail2ban|ntp|samba|nfs|vsftpd|postfix|dovecot|bind9|squid|haproxy|rsync|awscli|kubectl) ]]; then
      echo "$pkg" >> "$CUSTOM_PACKAGES_FILE"
    fi
  done < "$PACKAGES_FILE"
fi

CUSTOM_PACKAGES_COUNT=$(wc -l < "$CUSTOM_PACKAGES_FILE" 2>/dev/null || echo "0")
echo "Pacotes personalizados detectados: $CUSTOM_PACKAGES_COUNT" | tee -a "$INFOFILE"

if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
  echo "Principais pacotes personalizados:" | tee -a "$INFOFILE"
  head -20 "$CUSTOM_PACKAGES_FILE" | while read -r pkg; do
    echo "  - $pkg" | tee -a "$INFOFILE"
  done
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Serviços Habilitados
# -----------------------------
echo "=== Coletando serviços habilitados ===" | tee -a "$INFOFILE"

if command -v systemctl >/dev/null 2>&1; then
  systemctl list-unit-files --type=service --state=enabled --no-pager --no-legend | awk '{print $1}' > "$SERVICES_FILE"
  echo "Serviços habilitados:" | tee -a "$INFOFILE"
  head -10 "$SERVICES_FILE" | while read -r service; do
    echo "  - $service" | tee -a "$INFOFILE"
  done
elif command -v chkconfig >/dev/null 2>&1; then
  chkconfig --list 2>/dev/null | grep ":on" | awk '{print $1}' > "$SERVICES_FILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Coletar Crontabs
# -----------------------------
echo "=== Coletando crontabs ===" | tee -a "$INFOFILE"

# Crontab do root
if [ -f /var/spool/cron/root ] || [ -f /var/spool/cron/crontabs/root ]; then
  echo "# ROOT CRONTAB" >> "$CRONTAB_FILE"
  (crontab -l -u root 2>/dev/null || cat /var/spool/cron/root 2>/dev/null || cat /var/spool/cron/crontabs/root 2>/dev/null) >> "$CRONTAB_FILE"
  echo "" >> "$CRONTAB_FILE"
fi

# Crontabs de outros usuários
for user_cron in /var/spool/cron/* /var/spool/cron/crontabs/*; do
  if [ -f "$user_cron" ]; then
    username=$(basename "$user_cron")
    echo "# CRONTAB DO USUÁRIO: $username" >> "$CRONTAB_FILE"
    cat "$user_cron" >> "$CRONTAB_FILE"
    echo "" >> "$CRONTAB_FILE"
  fi
done

# Cron do sistema
if [ -d /etc/cron.d ]; then
  echo "# CRON.D FILES" >> "$CRONTAB_FILE"
  for cronfile in /etc/cron.d/*; do
    if [ -f "$cronfile" ]; then
      echo "## $(basename $cronfile)" >> "$CRONTAB_FILE"
      cat "$cronfile" >> "$CRONTAB_FILE"
      echo "" >> "$CRONTAB_FILE"
    fi
  done
fi

if [ -s "$CRONTAB_FILE" ]; then
  echo "Crontabs coletados em: $CRONTAB_FILE" | tee -a "$INFOFILE"
else
  echo "Nenhum crontab encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Configurações Importantes
# -----------------------------
echo "=== Detectando arquivos de configuração importantes ===" | tee -a "$INFOFILE"

IMPORTANT_CONFIGS=(
  "/etc/httpd/conf/httpd.conf"
  "/etc/nginx/nginx.conf"
  "/etc/php.ini"
  "/etc/my.cnf"
  "/etc/mysql/my.cnf"
  "/etc/postgresql/*/main/postgresql.conf"
  "/etc/redis.conf"
  "/etc/redis/redis.conf"
  "/etc/postfix/main.cf"
  "/etc/dovecot/dovecot.conf"
  "/etc/ssh/sshd_config"
  "/etc/sudoers"
  "/etc/hosts"
  "/etc/resolv.conf"
  "/etc/fstab"
  "/etc/exports"
  "/etc/samba/smb.conf"
  "/etc/bind/named.conf"
  "/etc/firewalld/zones/*.xml"
  "/etc/iptables/rules.v4"
  "/etc/fail2ban/jail.local"
  "/etc/logrotate.d/*"
  "/etc/systemd/system/*.service"
)

for config in "${IMPORTANT_CONFIGS[@]}"; do
  for file in $config; do
    if [ -f "$file" ]; then
      echo "$file" >> "$CONFIGS_FILE"
    fi
  done
done

if [ -s "$CONFIGS_FILE" ]; then
  echo "Arquivos de configuração importantes encontrados:" | tee -a "$INFOFILE"
  head -10 "$CONFIGS_FILE" | while read -r config; do
    echo "  - $config" | tee -a "$INFOFILE"
  done
else
  echo "Nenhum arquivo de configuração especial encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Usuários do Sistema
# -----------------------------
echo "=== Coletando usuários do sistema ===" | tee -a "$INFOFILE"
USERFILE="detected_users.txt"

awk -F: '$3 >= 1000 && $3 < 65534 {print $1":"$3":"$6":"$7}' /etc/passwd > "$USERFILE"
if [ -s "$USERFILE" ]; then
  echo "Usuários personalizados encontrados:" | tee -a "$INFOFILE"
  cat "$USERFILE" | while IFS=: read -r uname uid home shell; do
    echo "  - $uname (UID: $uid, Home: $home, Shell: $shell)" | tee -a "$INFOFILE"
  done
else
  echo "Nenhum usuário personalizado encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Definir diretórios de interesse
# -----------------------------
DEPLOY_DIRS=(/opt /srv /var/www /etc /usr/local /home/<USER>/app /var/lib/mysql /var/lib/postgresql)

# -----------------------------
# Detectar Portas em Uso
# -----------------------------
echo "=== Detectando portas em uso ===" | tee -a "$INFOFILE"

if command -v ss >/dev/null 2>&1; then
  PORTS=$(ss -tuln | awk 'NR>1 {gsub(/.*:/,"",$5); print $5}' | sort -n -u | tr '\n' ' ')
else
  PORTS=$(netstat -tuln 2>/dev/null | awk 'NR>2 {gsub(/.*:/,"",$4); print $4}' | sort -n -u | tr '\n' ' ')
fi

echo "Portas detectadas: $PORTS" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Gerar Dockerfile Avançado
# -----------------------------
echo "=== Gerando Dockerfile avançado ===" | tee -a "$INFOFILE"

{
  echo "# Generated by Enhanced Server Analysis Script"
  echo "# Date: $(date)"
  echo "# Base System: $OS_ID $OS_VER"
  echo "# Custom Packages: $CUSTOM_PACKAGES_COUNT"
  echo ""
  echo "FROM ${BASE_IMAGE}"
  echo ""
  echo "LABEL maintainer=\"server-replica@local\""
  echo "LABEL description=\"Replica of production server $(hostname)\""
  echo "LABEL created=\"$(date -I)\""
  echo ""
  echo "ENV container docker"
  echo "ENV DEBIAN_FRONTEND=noninteractive"
  echo ""

  # Instalar repositórios adicionais primeiro
  if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]]; then
    echo "# Instalar repositórios adicionais"
    echo "RUN yum -y update && yum -y install epel-release yum-utils"
    
    # Detectar se tem repositórios Remi, IUS, etc.
    if rpm -qa | grep -q remi-release; then
      echo "RUN yum -y install https://rpms.remirepo.net/enterprise/remi-release-\$(rpm -E %rhel).rpm"
    fi
    if rpm -qa | grep -q ius-release; then
      echo "RUN yum -y install https://repo.ius.io/ius-release-el\$(rpm -E %rhel).rpm"
    fi
    echo ""
  fi

  # Instalar pacotes personalizados
  if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
    echo "# Instalar pacotes personalizados detectados"
    
    if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]]; then
      # Agrupar pacotes em chunks para evitar linhas muito longas
      CHUNK_SIZE=10
      i=0
      while IFS= read -r pkg && [ -n "$pkg" ]; do
        if [ $((i % CHUNK_SIZE)) -eq 0 ]; then
          if [ $i -gt 0 ]; then
            echo " && yum clean all"
          fi
          echo -n "RUN yum -y install $pkg"
        else
          echo -n " $pkg"
        fi
        i=$((i+1))
      done < "$CUSTOM_PACKAGES_FILE"
      
      if [ $i -gt 0 ]; then
        echo " && yum clean all"
      fi
      
    elif [[ "$OS_ID" =~ ubuntu|debian ]]; then
      echo "RUN apt-get update && apt-get install -y \\"
      while IFS= read -r pkg && [ -n "$pkg" ]; do
        echo "    $pkg \\"
      done < "$CUSTOM_PACKAGES_FILE"
      echo " && apt-get clean && rm -rf /var/lib/apt/lists/*"
    fi
    echo ""
  fi

  # Criar usuários
  if [ -s "$USERFILE" ]; then
    echo "# Criar usuários detectados"
    while IFS=: read -r uname uid home shell; do
      [ -z "$uname" ] && continue
      echo "RUN groupadd -g ${uid} ${uname} 2>/dev/null || true"
      echo "RUN useradd -m -u ${uid} -g ${uid} -s ${shell} -d ${home} ${uname} 2>/dev/null || true"
    done < "$USERFILE"
    echo ""
  fi

  # Configurar crontabs
  if [ -s "$CRONTAB_FILE" ]; then
    echo "# Configurar crontabs"
    echo "RUN yum -y install cronie || apt-get update && apt-get install -y cron"
    echo "COPY crontabs.txt /tmp/crontabs.txt"
    echo "RUN while IFS= read -r line; do"
    echo "  if [[ \$line == \"# ROOT CRONTAB\" ]]; then"
    echo "    mode=\"root\""
    echo "  elif [[ \$line == \"# CRONTAB DO USUÁRIO:\"* ]]; then"
    echo "    mode=\$(echo \"\$line\" | cut -d: -f2 | tr -d ' ')"
    echo "  elif [[ \$line != \"#\"* ]] && [[ -n \"\$line\" ]]; then"
    echo "    echo \"\$line\" | crontab -u \${mode:-root} - 2>/dev/null || true"
    echo "  fi"
    echo "done < /tmp/crontabs.txt"
    echo ""
  fi

  # Copiar diretórios importantes
  echo "# Copiar diretórios de aplicação e configuração"
  for d in "${DEPLOY_DIRS[@]}"; do
    for realdir in $d; do
      if [ -d "$realdir" ]; then
        # Verificar se o diretório tem conteúdo relevante
        if [ "$(find "$realdir" -type f | wc -l)" -gt 0 ]; then
          echo "COPY ./backup$(echo $realdir) $realdir/"
        fi
      fi
    done
  done
  echo ""

  # Configurar serviços
  if [ -s "$SERVICES_FILE" ]; then
    echo "# Habilitar serviços detectados"
    while read -r service; do
      if [[ "$service" != *"@"* ]] && [[ "$service" != "getty"* ]]; then
        echo "RUN systemctl enable $service 2>/dev/null || true"
      fi
    done < "$SERVICES_FILE"
    echo ""
  fi

  # Expor portas
  if [ -n "$PORTS" ]; then
    echo "# Expor portas detectadas"
    for p in $PORTS; do
      pn=$(echo "$p" | sed 's/[^0-9].*$//')
      if [ -n "$pn" ] && [ "$pn" -gt 0 ] && [ "$pn" -lt 65536 ]; then
        echo "EXPOSE ${pn}"
      fi
    done
    echo ""
  fi

  # Volumes para dados persistentes
  echo "# Volumes para dados persistentes"
  echo "VOLUME [\"/var/log\", \"/var/lib/mysql\", \"/var/www\", \"/etc\"]"
  echo ""

  # Healthcheck
  echo "# Healthcheck básico"
  echo "HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\"
  echo "  CMD curl -f http://localhost/ || exit 1"
  echo ""

  # Comando de inicialização
  echo "# Script de inicialização"
  echo "COPY docker-entrypoint.sh /usr/local/bin/"
  echo "RUN chmod +x /usr/local/bin/docker-entrypoint.sh"
  echo ""
  echo "ENTRYPOINT [\"/usr/local/bin/docker-entrypoint.sh\"]"
  echo "CMD [\"/sbin/init\"]"
  
} > "$DOCKERFILE"

# -----------------------------
# Gerar Script de Entrypoint
# -----------------------------
cat > "docker-entrypoint.sh" << 'EOF'
#!/bin/bash

# Docker Entrypoint Script
# Inicializa serviços e configurações

# Iniciar cron
if command -v crond >/dev/null 2>&1; then
    crond
elif command -v cron >/dev/null 2>&1; then
    cron
fi

# Iniciar outros serviços essenciais
if command -v systemctl >/dev/null 2>&1; then
    systemctl start rsyslog 2>/dev/null || true
fi

# Executar comando passado como argumento
exec "$@"
EOF

chmod +x "docker-entrypoint.sh"

# -----------------------------
# Gerar Scripts de Backup
# -----------------------------
cat > "backup_server.sh" << 'EOF'
#!/bin/bash

# Script para fazer backup dos diretórios importantes
# Execute este script no servidor original antes de usar o Dockerfile

BACKUP_DIR="./backup"
mkdir -p "$BACKUP_DIR"

DIRS_TO_BACKUP=(/opt /srv /var/www /etc /usr/local /home/<USER>/app)

for dir in "${DIRS_TO_BACKUP[@]}"; do
    for realdir in $dir; do
        if [ -d "$realdir" ]; then
            echo "Fazendo backup de $realdir..."
            mkdir -p "$BACKUP_DIR$realdir"
            rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
                  "$realdir/" "$BACKUP_DIR$realdir/" 2>/dev/null || true
        fi
    done
done

echo "Backup completo em: $BACKUP_DIR"
EOF

chmod +x "backup_server.sh"

# -----------------------------
# Resumo Final
# -----------------------------
echo "================================" | tee -a "$INFOFILE"
echo "ANÁLISE COMPLETA FINALIZADA" | tee -a "$INFOFILE"
echo "================================" | tee -a "$INFOFILE"
echo "Arquivos gerados:" | tee -a "$INFOFILE"
echo "  - $DOCKERFILE (Dockerfile principal)" | tee -a "$INFOFILE"
echo "  - docker-entrypoint.sh (Script de inicialização)" | tee -a "$INFOFILE"
echo "  - backup_server.sh (Script de backup)" | tee -a "$INFOFILE"
echo "  - $INFOFILE (Log da análise)" | tee -a "$INFOFILE"
echo "  - $PACKAGES_FILE (Lista completa de pacotes)" | tee -a "$INFOFILE"
echo "  - $CUSTOM_PACKAGES_FILE (Pacotes personalizados)" | tee -a "$INFOFILE"
echo "  - $SERVICES_FILE (Serviços habilitados)" | tee -a "$INFOFILE"
echo "  - $CRONTAB_FILE (Crontabs coletados)" | tee -a "$INFOFILE"
echo "  - $CONFIGS_FILE (Arquivos de configuração)" | tee -a "$INFOFILE"
echo "  - $USERFILE (Usuários personalizados)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"
echo "PRÓXIMOS PASSOS:" | tee -a "$INFOFILE"
echo "1. Execute './backup_server.sh' para fazer backup dos dados" | tee -a "$INFOFILE"
echo "2. Copie os arquivos de crontab: cp $CRONTAB_FILE crontabs.txt" | tee -a "$INFOFILE"
echo "3. Construa a imagem: docker build -t servidor-replica ." | tee -a "$INFOFILE"
echo "4. Execute o container com volumes persistentes" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# Copiar crontabs para uso no Dockerfile
if [ -s "$CRONTAB_FILE" ]; then
    cp "$CRONTAB_FILE" crontabs.txt
fi

echo "Script finalizado com sucesso!" | tee -a "$INFOFILE"