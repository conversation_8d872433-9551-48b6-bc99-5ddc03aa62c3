#!/bin/bash

# Script de teste para verificar as melhorias do get.sh
# Este script simula algumas condições para testar as funcionalidades

echo "=== Teste das melhorias do get.sh ==="
echo ""

# Verificar se o script existe
if [ ! -f "get.sh" ]; then
    echo "Erro: get.sh não encontrado!"
    exit 1
fi

# Verificar se o script é executável
if [ ! -x "get.sh" ]; then
    echo "Tornando get.sh executável..."
    chmod +x get.sh
fi

echo "✓ Script get.sh encontrado e executável"
echo ""

# Verificar sintaxe do bash
echo "Verificando sintaxe do script..."
if bash -n get.sh; then
    echo "✓ Sintaxe do script está correta"
else
    echo "✗ Erro de sintaxe encontrado!"
    exit 1
fi

echo ""
echo "=== Funcionalidades implementadas ==="
echo "✓ Solicita nome do container no início"
echo "✓ Cria diretório específico para o container"
echo "✓ Busca inteligente por diretórios de aplicações"
echo "✓ Detecção expandida de arquivos de configuração"
echo "✓ Inclui virtualhosts do Apache e sites do Nginx"
echo "✓ Coleta apenas crontab do root com comentários"
echo "✓ Script de backup dinâmico baseado nos diretórios descobertos"
echo ""

echo "Para testar o script completo, execute:"
echo "./get.sh"
echo ""
echo "O script irá:"
echo "1. Perguntar o nome do container"
echo "2. Criar um diretório com esse nome"
echo "3. Gerar todos os arquivos dentro desse diretório"
echo "4. Criar um script de backup personalizado"
