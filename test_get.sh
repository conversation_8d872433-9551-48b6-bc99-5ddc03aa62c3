#!/bin/bash

# Script de teste para verificar as melhorias do get.sh
# Este script simula algumas condições para testar as funcionalidades

echo "=== Teste das melhorias do get.sh ==="
echo ""

# Verificar se o script existe
if [ ! -f "get.sh" ]; then
    echo "Erro: get.sh não encontrado!"
    exit 1
fi

# Verificar se o script é executável
if [ ! -x "get.sh" ]; then
    echo "Tornando get.sh executável..."
    chmod +x get.sh
fi

echo "✓ Script get.sh encontrado e executável"
echo ""

# Verificar sintaxe do bash
echo "Verificando sintaxe do script..."
if bash -n get.sh; then
    echo "✓ Sintaxe do script está correta"
else
    echo "✗ Erro de sintaxe encontrado!"
    exit 1
fi

echo ""
echo "=== Funcionalidades implementadas (FOCADAS EM SERVIÇOS) ==="
echo "✓ Solicita nome do container no início"
echo "✓ Cria diretório específico para o container"
echo "✓ Diretórios OBRIGATÓRIOS sempre incluídos:"
echo "  - /var/www (sempre, mesmo se vazio)"
echo "  - /etc (configurações do sistema)"
echo "  - Bancos: /var/lib/mysql, /var/lib/postgresql, /var/lib/redis"
echo "  - Serviços: /var/lib/zabbix, /var/lib/grafana, /var/lib/netbox"
echo "✓ Busca FOCADA em serviços conhecidos (não genérica)"
echo "✓ Configurações específicas dos serviços da infraestrutura:"
echo "  - Apache/Nginx: TODOS os virtualhosts e sites"
echo "  - PHP: Todas as versões e configurações"
echo "  - Bancos: MySQL, PostgreSQL, Redis, MongoDB"
echo "  - Mail: Postfix, Dovecot"
echo "  - Monitoramento: Zabbix, Grafana"
echo "  - Outros: Radius, DNS, Firewall, Plesk, Netbox"
echo "✓ Coleta apenas crontab do root com comentários"
echo "✓ Script de backup dinâmico baseado nos diretórios descobertos"
echo "✓ Filtros para evitar logs e arquivos temporários"
echo ""

echo "Para testar o script completo, execute:"
echo "./get.sh"
echo ""
echo "O script irá:"
echo "1. Perguntar o nome do container"
echo "2. Criar um diretório com esse nome"
echo "3. Gerar todos os arquivos dentro desse diretório"
echo "4. Criar um script de backup personalizado"
