=== Iniciando análise completa do servidor ===
Container: noc-dev
Data/Hora: Tue Sep 16 17:11:11 -03 2025

=== Detectando Sistema Operacional ===
Sistema: centos 7
Base image selecionada: centos:7

=== Coletando lista de pacotes instalados ===
Sistema baseado em RPM detectado
Total de pacotes instalados: 520
=== Detectando pacotes personalizados ===
=== Repositórios habilitados ===
Loading mirror speeds from cached hostfile
 * epel: d2lzkl7pfhq30w.cloudfront.net
!base/x86_64                 CentOS-7 - Base                              10072
!epel/x86_64                 Extra Packages for Enterprise Linux 7 - x86_ 13791
!extras/x86_64               CentOS-7 - Extras                              526
!updates/x86_64              CentOS-7 - Updates                            6173
!webtatic/x86_64             Webtatic Repository EL7 - x86_64               789
!zabbix/x86_64               Zabbix Official Repository - x86_64            236
!zabbix-non-supported/x86_64 Zabbix Official Repository non-supported - x     6
Pacotes personalizados detectados: 105
Principais pacotes personalizados:
  - autoconf
  - automake
  - bind-license
  - certbot
  - emacs-filesystem
  - epel-release
  - firewalld
  - firewalld-filesystem
  - gcc
  - gcc-c++
  - gcc-gfortran
  - git
  - git-core
  - git-core-doc
  - git-daemon
  - htop
  - httpd
  - httpd-tools
  - iptables
  - libtool

=== Coletando serviços habilitados ===
Serviços habilitados:
  - autovt@.service
  - bacula-fd.service
  - crond.service
  - getty@.service
  - git-daemon.service
  - httpd.service
  - nslcd.service
  - postgresql-9.6.service
  - qemu-guest-agent.service
  - sshd.service

=== Descobrindo diretórios de aplicações ===
Adicionando diretórios obrigatórios...
  Obrigatório: /var/www
  Obrigatório: /etc
  Obrigatório: /var/lib/pgsql
  Obrigatório: /var/spool/mail
  Obrigatório: /var/mail
Buscando diretórios de aplicações específicas...
Analisando /srv...
  Encontrado: /srv
  Encontrado: /srv/flow-collect
  Encontrado: /srv/flow-collect/include
Analisando /opt...
  Encontrado: /opt
Analisando /usr/local...
  Encontrado: /usr/local
Analisando /home...
Analisando /var/lib...
Total de diretórios selecionados: 10
Diretórios selecionados para backup:
  - /etc
  - /opt
  - /srv
  - /srv/flow-collect
  - /srv/flow-collect/include
  - /usr/local
  - /var/lib/pgsql
  - /var/mail
  - /var/spool/mail
  - /var/www

=== Coletando crontab do root ===
Crontab do root coletada via 'crontab -l'

=== Detectando arquivos de configuração importantes ===
Buscando configurações dos serviços principais...
  - Apache/HTTPD...
  - Nginx...
  - PHP...
Buscando configurações específicas nos diretórios de aplicações...
Filtrando arquivos de configuração...
Arquivos de configuração selecionados: 1060
Principais arquivos de configuração:
  - /etc/bacula/bacula-fd.conf
  - /etc/cron.d/0hourly
  - /etc/dbus-1/session.conf
  - /etc/dbus-1/system.conf
  - /etc/dbus-1/system.d/FirewallD.conf
  - /etc/dbus-1/system.d/org.freedesktop.hostname1.conf
  - /etc/dbus-1/system.d/org.freedesktop.import1.conf
  - /etc/dbus-1/system.d/org.freedesktop.locale1.conf
  - /etc/dbus-1/system.d/org.freedesktop.login1.conf
  - /etc/dbus-1/system.d/org.freedesktop.machine1.conf
  - /etc/dbus-1/system.d/org.freedesktop.systemd1.conf
  - /etc/dbus-1/system.d/org.freedesktop.timedate1.conf
  - /etc/dbus-1/system.d/org.selinux.conf
  - /etc/depmod.d/dist.conf
  - /etc/dracut.conf
  - /etc/environment
  - /etc/exports
  - /etc/firewalld/firewalld.conf
  - /etc/fonts/conf.d/25-no-bitmap-fedora.conf
  - /etc/fonts/fonts.conf
  - /etc/fstab
  - /etc/host.conf
  - /etc/hosts
  - /etc/httpd/conf.d/README
  - /etc/httpd/conf.d/atendimento.conf
  - /etc/httpd/conf.d/autoindex.conf
  - /etc/httpd/conf.d/callhistory.conf
  - /etc/httpd/conf.d/ftth.conf
  - /etc/httpd/conf.d/git.conf
  - /etc/httpd/conf.d/mailinfo.conf
  ... e mais 1030 arquivos

=== Coletando usuários do sistema ===
Usuários personalizados encontrados:
  - docs (UID: 1000, Home: /home/<USER>/bin/bash)
  - git (UID: 1001, Home: /home/<USER>/bin/bash)
  - npm (UID: 1002, Home: /home/<USER>/bin/bash)
  - noc (UID: 1003, Home: /home/<USER>/bin/bash)

=== Preparando lista de diretórios para backup ===
Diretórios selecionados para backup:
  - /etc
  - /etc
  - /opt
  - /srv
  - /srv/flow-collect
  - /srv/flow-collect/include
  - /usr/local
  - /var/lib/pgsql
  - /var/mail
  - /var/spool/mail
  - /var/www

=== Detectando portas em uso ===
Portas detectadas: 22 80 443 ************** 32790 43584 43751 57172 

=== Gerando Dockerfile avançado ===
================================
ANÁLISE COMPLETA FINALIZADA
================================
Container: noc-dev
Diretório: /home/<USER>/pnet-clone/noc-dev

Arquivos gerados:
  - output.Dockerfile (Dockerfile principal)
  - docker-entrypoint.sh (Script de inicialização)
  - backup_server.sh (Script de backup dinâmico)
  - docker_info.log (Log da análise)
  - detected_packages.txt (Lista completa de pacotes)
  - custom_packages.txt (Pacotes personalizados)
  - detected_services.txt (Serviços habilitados)
  - detected_crontabs.txt (Crontab do root)
  - detected_configs.txt (Arquivos de configuração)
  - application_directories.txt (Diretórios de aplicação)
  - detected_users.txt (Usuários personalizados)

Estatísticas:
  - Diretórios de aplicação: 10
  - Arquivos de configuração: 1060
  - Pacotes personalizados: 105

PRÓXIMOS PASSOS:
1. Execute './backup_server.sh' para fazer backup dos dados
2. Construa a imagem: docker build -t noc-dev .
3. Execute o container: docker run -d --name noc-dev noc-dev

Crontab copiada para crontabs.txt
Script finalizado com sucesso!
