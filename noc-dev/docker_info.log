=== Iniciando análise completa do servidor ===
Container: noc-dev
Data/Hora: Tue Sep 16 17:03:14 -03 2025

=== Detectando Sistema Operacional ===
Sistema: centos 7
Base image selecionada: centos:7

=== Coletando lista de pacotes instalados ===
Sistema baseado em RPM detectado
Total de pacotes instalados: 520
=== Detectando pacotes personalizados ===
=== Repositórios habilitados ===
Determining fastest mirrors
 * epel: d2lzkl7pfhq30w.cloudfront.net
!base/x86_64                 CentOS-7 - Base                              10072
!epel/x86_64                 Extra Packages for Enterprise Linux 7 - x86_ 13791
!extras/x86_64               CentOS-7 - Extras                              526
!updates/x86_64              CentOS-7 - Updates                            6173
!webtatic/x86_64             Webtatic Repository EL7 - x86_64               789
!zabbix/x86_64               Zabbix Official Repository - x86_64            236
!zabbix-non-supported/x86_64 Zabbix Official Repository non-supported - x     6
Pacotes personalizados detectados: 105
Principais pacotes personalizados:
  - autoconf
  - automake
  - bind-license
  - certbot
  - emacs-filesystem
  - epel-release
  - firewalld
  - firewalld-filesystem
  - gcc
  - gcc-c++
  - gcc-gfortran
  - git
  - git-core
  - git-core-doc
  - git-daemon
  - htop
  - httpd
  - httpd-tools
  - iptables
  - libtool

=== Coletando serviços habilitados ===
Serviços habilitados:
  - autovt@.service
  - bacula-fd.service
  - crond.service
  - getty@.service
  - git-daemon.service
  - httpd.service
  - nslcd.service
  - postgresql-9.6.service
  - qemu-guest-agent.service
  - sshd.service

=== Descobrindo diretórios de aplicações ===
Buscando diretórios de aplicações...
Analisando /var/www...
Analisando /srv...
  Encontrado: /srv
  Encontrado: /srv/flow-collect
  Encontrado: /srv/flow-collect/include
Analisando /opt...
Analisando /usr/local...
  Encontrado: /usr/local
Analisando /home...
  Encontrado: /home/<USER>
  Encontrado: /home/<USER>/pnet-clone
Analisando /var/lib...
Analisando /usr/share...
  Encontrado: /usr/share
  Encontrado: /usr/share/gcc-4.8.2/python
  Encontrado: /usr/share/gcc-4.8.2/python/libstdcxx
  Encontrado: /usr/share/gdb/auto-load
  Encontrado: /usr/share/gdb/auto-load/usr
  Encontrado: /usr/share/gdb/python
  Encontrado: /usr/share/gdb/python/gdb
  Encontrado: /usr/share/yum-cli
  Encontrado: /usr/share/Modules
  Encontrado: /usr/share/Modules/bin
  Encontrado: /usr/share/Modules/init
  Encontrado: /usr/share/automake-1.13
  Encontrado: /usr/share/git-core
  Encontrado: /usr/share/git-core/contrib
  Encontrado: /usr/share/git-core/contrib/completion
  Encontrado: /usr/share/httpd
  Encontrado: /usr/share/httpd/icons
  Encontrado: /usr/share/httpd/icons/small
  Encontrado: /usr/share/httpd/noindex
  Encontrado: /usr/share/libtool
  Encontrado: /usr/share/libtool/config
  Encontrado: /usr/share/pear
  Encontrado: /usr/share/pear/Archive
  Encontrado: /usr/share/pear/Console
  Encontrado: /usr/share/pear/OS
  Encontrado: /usr/share/pear/PEAR
  Encontrado: /usr/share/pear/PEAR/ChannelFile
  Encontrado: /usr/share/pear/PEAR/Command
  Encontrado: /usr/share/pear/PEAR/Downloader
  Encontrado: /usr/share/pear/PEAR/Frontend
  Encontrado: /usr/share/pear/PEAR/Installer
  Encontrado: /usr/share/pear/PEAR/PackageFile
  Encontrado: /usr/share/pear/PEAR/REST
  Encontrado: /usr/share/pear/PEAR/Task
  Encontrado: /usr/share/pear/PEAR/Validator
  Encontrado: /usr/share/pear/Structures
  Encontrado: /usr/share/pear/Structures/Graph
  Encontrado: /usr/share/pear/XML
  Encontrado: /usr/share/pear/PEAR2
  Encontrado: /usr/share/pear/PEAR2/Cache
  Encontrado: /usr/share/pear/PEAR2/Console
  Encontrado: /usr/share/pear/PEAR2/Net
  Encontrado: /usr/share/perl5
  Encontrado: /usr/share/perl5/Unicode/Collate
  Encontrado: /usr/share/perl5/unicore
  Encontrado: /usr/share/perl5/unicore/To
  Encontrado: /usr/share/perl5/unicore/lib
  Encontrado: /usr/share/perl5/vendor_perl
  Encontrado: /usr/share/perl5/vendor_perl/Business
  Encontrado: /usr/share/php/Fedora
  Encontrado: /usr/share/php/Fedora/Autoloader
  Encontrado: /usr/share/php/Seld
  Encontrado: /usr/share/php/Seld/JsonLint
  Encontrado: /usr/share/swig/2.0.10
  Encontrado: /usr/share/swig/2.0.10/perl5
  Encontrado: /usr/share/swig/2.0.10/ruby
  Encontrado: /usr/share/tests/pear/Structures_Graph
  Encontrado: /usr/share/tests/pear/XML_Util
  Encontrado: /usr/share/vim/vim74
  Encontrado: /usr/share/vim/vim74/doc
  Encontrado: /usr/share/vim/vim74/macros
  Encontrado: /usr/share/vim/vim74/tutor
Total de diretórios de aplicação encontrados: 68

=== Coletando crontab do root ===
Crontab do root coletada via 'crontab -l'

=== Detectando arquivos de configuração importantes ===
Buscando configurações do Apache...
Buscando configurações do Nginx...
Buscando configurações do PHP...
Buscando configurações de bancos de dados...
Buscando configurações nos diretórios de aplicações...
Arquivos de configuração encontrados: 3346
Principais arquivos de configuração:
  - /etc/cron.d/0hourly
  - /etc/environment
  - /etc/exports
  - /etc/fstab
  - /etc/hosts
  - /etc/httpd/conf.d/atendimento.conf
  - /etc/httpd/conf.d/autoindex.conf
  - /etc/httpd/conf.d/callhistory.conf
  - /etc/httpd/conf.d/ftth.conf
  - /etc/httpd/conf.d/git.conf
  - /etc/httpd/conf.d/mailinfo.conf
  - /etc/httpd/conf.d/noc.conf
  - /etc/httpd/conf.d/noc_443.conf
  - /etc/httpd/conf.d/php.conf
  - /etc/httpd/conf.d/ssl.conf
  - /etc/httpd/conf.d/userdir.conf
  - /etc/httpd/conf.d/welcome.conf
  - /etc/httpd/conf.modules.d/00-base.conf
  - /etc/httpd/conf.modules.d/00-dav.conf
  - /etc/httpd/conf.modules.d/00-lua.conf

=== Coletando usuários do sistema ===
Usuários personalizados encontrados:
  - docs (UID: 1000, Home: /home/<USER>/bin/bash)
  - git (UID: 1001, Home: /home/<USER>/bin/bash)
  - npm (UID: 1002, Home: /home/<USER>/bin/bash)
  - noc (UID: 1003, Home: /home/<USER>/bin/bash)

=== Preparando lista de diretórios para backup ===
Diretórios selecionados para backup:
  - /etc
  - /home/<USER>
  - /home/<USER>/pnet-clone
  - /srv
  - /srv/flow-collect
  - /srv/flow-collect/include
  - /usr/local
  - /usr/share
  - /usr/share/Modules
  - /usr/share/Modules/bin
  - /usr/share/Modules/init
  - /usr/share/automake-1.13
  - /usr/share/gcc-4.8.2/python
  - /usr/share/gcc-4.8.2/python/libstdcxx
  - /usr/share/gdb/auto-load
  - /usr/share/gdb/auto-load/usr
  - /usr/share/gdb/python
  - /usr/share/gdb/python/gdb
  - /usr/share/git-core
  - /usr/share/git-core/contrib
  - /usr/share/git-core/contrib/completion
  - /usr/share/httpd
  - /usr/share/httpd/icons
  - /usr/share/httpd/icons/small
  - /usr/share/httpd/noindex
  - /usr/share/libtool
  - /usr/share/libtool/config
  - /usr/share/pear
  - /usr/share/pear/Archive
  - /usr/share/pear/Console
  - /usr/share/pear/OS
  - /usr/share/pear/PEAR
  - /usr/share/pear/PEAR/ChannelFile
  - /usr/share/pear/PEAR/Command
  - /usr/share/pear/PEAR/Downloader
  - /usr/share/pear/PEAR/Frontend
  - /usr/share/pear/PEAR/Installer
  - /usr/share/pear/PEAR/PackageFile
  - /usr/share/pear/PEAR/REST
  - /usr/share/pear/PEAR/Task
  - /usr/share/pear/PEAR/Validator
  - /usr/share/pear/PEAR2
  - /usr/share/pear/PEAR2/Cache
  - /usr/share/pear/PEAR2/Console
  - /usr/share/pear/PEAR2/Net
  - /usr/share/pear/Structures
  - /usr/share/pear/Structures/Graph
  - /usr/share/pear/XML
  - /usr/share/perl5
  - /usr/share/perl5/Unicode/Collate
  - /usr/share/perl5/unicore
  - /usr/share/perl5/unicore/To
  - /usr/share/perl5/unicore/lib
  - /usr/share/perl5/vendor_perl
  - /usr/share/perl5/vendor_perl/Business
  - /usr/share/php/Fedora
  - /usr/share/php/Fedora/Autoloader
  - /usr/share/php/Seld
  - /usr/share/php/Seld/JsonLint
  - /usr/share/swig/2.0.10
  - /usr/share/swig/2.0.10/perl5
  - /usr/share/swig/2.0.10/ruby
  - /usr/share/tests/pear/Structures_Graph
  - /usr/share/tests/pear/XML_Util
  - /usr/share/vim/vim74
  - /usr/share/vim/vim74/doc
  - /usr/share/vim/vim74/macros
  - /usr/share/vim/vim74/tutor
  - /usr/share/yum-cli

=== Detectando portas em uso ===
Portas detectadas: 22 80 443 4000 9102 9418 32790 43584 43751 57172 

=== Gerando Dockerfile avançado ===
================================
ANÁLISE COMPLETA FINALIZADA
================================
Container: noc-dev
Diretório: /home/<USER>/pnet-clone/noc-dev

Arquivos gerados:
  - output.Dockerfile (Dockerfile principal)
  - docker-entrypoint.sh (Script de inicialização)
  - backup_server.sh (Script de backup dinâmico)
  - docker_info.log (Log da análise)
  - detected_packages.txt (Lista completa de pacotes)
  - custom_packages.txt (Pacotes personalizados)
  - detected_services.txt (Serviços habilitados)
  - detected_crontabs.txt (Crontab do root)
  - detected_configs.txt (Arquivos de configuração)
  - application_directories.txt (Diretórios de aplicação)
  - detected_users.txt (Usuários personalizados)

Estatísticas:
  - Diretórios de aplicação: 68
  - Arquivos de configuração: 3346
  - Pacotes personalizados: 105

PRÓXIMOS PASSOS:
1. Execute './backup_server.sh' para fazer backup dos dados
2. Construa a imagem: docker build -t noc-dev .
3. Execute o container: docker run -d --name noc-dev noc-dev

Crontab copiada para crontabs.txt
Script finalizado com sucesso!
