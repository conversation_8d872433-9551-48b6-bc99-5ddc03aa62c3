# Generated by Enhanced Server Analysis Script
# Date: Tue Sep 16 17:11:22 -03 2025
# Base System: centos 7
# Custom Packages: 105

FROM centos:7

LABEL maintainer="server-replica@local"
LABEL description="Replica of production server noc1-dev.pocos-net.com.br"
LABEL created="2025-09-16"

ENV container docker
ENV DEBIAN_FRONTEND=noninteractive

# Instalar repositórios adicionais
RUN yum -y update && yum -y install epel-release yum-utils

# Instalar pacotes personalizados detectados
RUN yum -y install autoconf automake bind-license certbot emacs-filesystem epel-release firewalld firewalld-filesystem gcc gcc-c++ && yum clean all
RUN yum -y install gcc-gfortran git git-core git-core-doc git-daemon htop httpd httpd-tools iptables libtool && yum clean all
RUN yum -y install make mariadb mariadb-libs nano nodejs ntpdate php-fedora-autoloader php-j<PERSON>lint php-pear php55w && yum clean all
RUN yum -y install php55w-bcmath php55w-cli php55w-common php55w-gd php55w-intl php55w-ldap php55w-mbstring php55w-mysql php55w-pdo php55w-pgsql && yum clean all
RUN yum -y install php55w-process php55w-xml pkgconfig postgresql postgresql-libs postgresql96 postgresql96-contrib postgresql96-devel postgresql96-libs postgresql96-server && yum clean all
RUN yum -y install python python-IPy python-augeas python-backports python-backports-ssl_match_hostname python-cffi python-chardet python-configobj python-decorator python-enum34 && yum clean all
RUN yum -y install python-firewall python-gobject-base python-idna python-iniparse python-ipaddress python-kitchen python-libs python-ndg_httpsclient python-ply python-pycparser && yum clean all
RUN yum -y install python-pycurl python-requests python-requests-toolbelt python-setuptools python-six python-slip python-slip-dbus python-srpm-macros python-urlgrabber python-urllib3 && yum clean all
RUN yum -y install python-zope-component python-zope-event python-zope-interface python2-acme python2-certbot python2-certbot-apache python2-configargparse python2-cryptography python2-distro python2-future && yum clean all
RUN yum -y install python2-josepy python2-mock python2-parsedatetime python2-pyasn1 python2-pyrfc3339 python2-six rsync vim-common vim-enhanced vim-filesystem && yum clean all
RUN yum -y install vsftpd webtatic-release zabbix-agent zabbix-release zabbix-sender && yum clean all

# Criar usuários detectados
RUN groupadd -g 1000 docs 2>/dev/null || true
RUN useradd -m -u 1000 -g 1000 -s /bin/bash -d /home/<USER>/dev/null || true
RUN groupadd -g 1001 git 2>/dev/null || true
RUN useradd -m -u 1001 -g 1001 -s /bin/bash -d /home/<USER>/dev/null || true
RUN groupadd -g 1002 npm 2>/dev/null || true
RUN useradd -m -u 1002 -g 1002 -s /bin/bash -d /home/<USER>/dev/null || true
RUN groupadd -g 1003 noc 2>/dev/null || true
RUN useradd -m -u 1003 -g 1003 -s /bin/bash -d /home/<USER>/dev/null || true

# Configurar crontab do root
RUN yum -y install cronie || apt-get update && apt-get install -y cron
COPY crontabs.txt /tmp/crontabs.txt
RUN crontab -u root /tmp/crontabs.txt 2>/dev/null || true

# Copiar diretórios de aplicação e configuração
COPY ./backup/etc /etc/
COPY ./backup/etc /etc/
COPY ./backup/opt /opt/
COPY ./backup/srv /srv/
COPY ./backup/srv/flow-collect /srv/flow-collect/
COPY ./backup/srv/flow-collect/include /srv/flow-collect/include/
COPY ./backup/var/lib/pgsql /var/lib/pgsql/
COPY ./backup/var/spool/mail /var/spool/mail/
COPY ./backup/var/www /var/www/

# Habilitar serviços detectados
RUN systemctl enable bacula-fd.service 2>/dev/null || true
RUN systemctl enable crond.service 2>/dev/null || true
RUN systemctl enable git-daemon.service 2>/dev/null || true
RUN systemctl enable httpd.service 2>/dev/null || true
RUN systemctl enable nslcd.service 2>/dev/null || true
RUN systemctl enable postgresql-9.6.service 2>/dev/null || true
RUN systemctl enable qemu-guest-agent.service 2>/dev/null || true
RUN systemctl enable sshd.service 2>/dev/null || true
RUN systemctl enable systemd-readahead-collect.service 2>/dev/null || true
RUN systemctl enable systemd-readahead-drop.service 2>/dev/null || true
RUN systemctl enable systemd-readahead-replay.service 2>/dev/null || true
RUN systemctl enable systemd-user-sessions.service 2>/dev/null || true
RUN systemctl enable zabbix-agent.service 2>/dev/null || true

# Expor portas detectadas
EXPOSE 22
EXPOSE 80
EXPOSE 443
EXPOSE 4000
EXPOSE 9102
EXPOSE 9418
EXPOSE 32790
EXPOSE 43584
EXPOSE 43751
EXPOSE 57172

# Volumes para dados persistentes
VOLUME ["/var/log", "/var/lib/mysql", "/var/www", "/etc"]

# Healthcheck básico
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Script de inicialização
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["/sbin/init"]
