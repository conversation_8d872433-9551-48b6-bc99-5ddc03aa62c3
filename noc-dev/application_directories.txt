/home/<USER>
/home/<USER>/pnet-clone
/srv
/srv/flow-collect
/srv/flow-collect/include
/usr/local
/usr/share
/usr/share/Modules
/usr/share/Modules/bin
/usr/share/Modules/init
/usr/share/automake-1.13
/usr/share/gcc-4.8.2/python
/usr/share/gcc-4.8.2/python/libstdcxx
/usr/share/gdb/auto-load
/usr/share/gdb/auto-load/usr
/usr/share/gdb/python
/usr/share/gdb/python/gdb
/usr/share/git-core
/usr/share/git-core/contrib
/usr/share/git-core/contrib/completion
/usr/share/httpd
/usr/share/httpd/icons
/usr/share/httpd/icons/small
/usr/share/httpd/noindex
/usr/share/libtool
/usr/share/libtool/config
/usr/share/pear
/usr/share/pear/Archive
/usr/share/pear/Console
/usr/share/pear/OS
/usr/share/pear/PEAR
/usr/share/pear/PEAR/ChannelFile
/usr/share/pear/PEAR/Command
/usr/share/pear/PEAR/Downloader
/usr/share/pear/PEAR/Frontend
/usr/share/pear/PEAR/Installer
/usr/share/pear/PEAR/PackageFile
/usr/share/pear/PEAR/REST
/usr/share/pear/PEAR/Task
/usr/share/pear/PEAR/Validator
/usr/share/pear/PEAR2
/usr/share/pear/PEAR2/Cache
/usr/share/pear/PEAR2/Console
/usr/share/pear/PEAR2/Net
/usr/share/pear/Structures
/usr/share/pear/Structures/Graph
/usr/share/pear/XML
/usr/share/perl5
/usr/share/perl5/Unicode/Collate
/usr/share/perl5/unicore
/usr/share/perl5/unicore/To
/usr/share/perl5/unicore/lib
/usr/share/perl5/vendor_perl
/usr/share/perl5/vendor_perl/Business
/usr/share/php/Fedora
/usr/share/php/Fedora/Autoloader
/usr/share/php/Seld
/usr/share/php/Seld/JsonLint
/usr/share/swig/2.0.10
/usr/share/swig/2.0.10/perl5
/usr/share/swig/2.0.10/ruby
/usr/share/tests/pear/Structures_Graph
/usr/share/tests/pear/XML_Util
/usr/share/vim/vim74
/usr/share/vim/vim74/doc
/usr/share/vim/vim74/macros
/usr/share/vim/vim74/tutor
/usr/share/yum-cli
