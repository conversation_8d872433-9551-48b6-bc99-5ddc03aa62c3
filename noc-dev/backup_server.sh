#!/bin/bash

# Script para fazer backup dos diretórios importantes
# Execute este script no servidor original antes de usar o Dockerfile
# Gerado automaticamente em Tue Sep 16 17:11:23 -03 2025

BACKUP_DIR="./backup"
mkdir -p "$BACKUP_DIR"

echo "=== Iniciando backup dos diretórios descobertos ==="
echo "Data/Hora: $(date)"
echo ""

# Diretórios descobertos automaticamente:
if [ -d "/etc" ]; then
    echo "Fazendo backup de /etc..."
    mkdir -p "$BACKUP_DIR/etc"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/etc/" "$BACKUP_DIR/etc/" 2>/dev/null || true
    echo "  Backup de /etc concluído"
fi

if [ -d "/etc" ]; then
    echo "Fazendo backup de /etc..."
    mkdir -p "$BACKUP_DIR/etc"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/etc/" "$BACKUP_DIR/etc/" 2>/dev/null || true
    echo "  Backup de /etc concluído"
fi

if [ -d "/opt" ]; then
    echo "Fazendo backup de /opt..."
    mkdir -p "$BACKUP_DIR/opt"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/opt/" "$BACKUP_DIR/opt/" 2>/dev/null || true
    echo "  Backup de /opt concluído"
fi

if [ -d "/srv" ]; then
    echo "Fazendo backup de /srv..."
    mkdir -p "$BACKUP_DIR/srv"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/srv/" "$BACKUP_DIR/srv/" 2>/dev/null || true
    echo "  Backup de /srv concluído"
fi

if [ -d "/srv/flow-collect" ]; then
    echo "Fazendo backup de /srv/flow-collect..."
    mkdir -p "$BACKUP_DIR/srv/flow-collect"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/srv/flow-collect/" "$BACKUP_DIR/srv/flow-collect/" 2>/dev/null || true
    echo "  Backup de /srv/flow-collect concluído"
fi

if [ -d "/srv/flow-collect/include" ]; then
    echo "Fazendo backup de /srv/flow-collect/include..."
    mkdir -p "$BACKUP_DIR/srv/flow-collect/include"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/srv/flow-collect/include/" "$BACKUP_DIR/srv/flow-collect/include/" 2>/dev/null || true
    echo "  Backup de /srv/flow-collect/include concluído"
fi

if [ -d "/usr/local" ]; then
    echo "Fazendo backup de /usr/local..."
    mkdir -p "$BACKUP_DIR/usr/local"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/local/" "$BACKUP_DIR/usr/local/" 2>/dev/null || true
    echo "  Backup de /usr/local concluído"
fi

if [ -d "/var/lib/pgsql" ]; then
    echo "Fazendo backup de /var/lib/pgsql..."
    mkdir -p "$BACKUP_DIR/var/lib/pgsql"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/var/lib/pgsql/" "$BACKUP_DIR/var/lib/pgsql/" 2>/dev/null || true
    echo "  Backup de /var/lib/pgsql concluído"
fi

if [ -d "/var/mail" ]; then
    echo "Fazendo backup de /var/mail..."
    mkdir -p "$BACKUP_DIR/var/mail"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/var/mail/" "$BACKUP_DIR/var/mail/" 2>/dev/null || true
    echo "  Backup de /var/mail concluído"
fi

if [ -d "/var/spool/mail" ]; then
    echo "Fazendo backup de /var/spool/mail..."
    mkdir -p "$BACKUP_DIR/var/spool/mail"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/var/spool/mail/" "$BACKUP_DIR/var/spool/mail/" 2>/dev/null || true
    echo "  Backup de /var/spool/mail concluído"
fi

if [ -d "/var/www" ]; then
    echo "Fazendo backup de /var/www..."
    mkdir -p "$BACKUP_DIR/var/www"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/var/www/" "$BACKUP_DIR/var/www/" 2>/dev/null || true
    echo "  Backup de /var/www concluído"
fi

echo ""
echo "=== Backup completo ==="
echo "Diretório de backup: $BACKUP_DIR"
echo "Tamanho total: $(du -sh $BACKUP_DIR 2>/dev/null | cut -f1)"
echo "Arquivos copiados: $(find $BACKUP_DIR -type f | wc -l)"
echo ""
echo "Para usar com Docker:"
echo "1. docker build -t $CONTAINER_NAME ."
echo "2. docker run -d --name $CONTAINER_NAME $CONTAINER_NAME"
