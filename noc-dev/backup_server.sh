#!/bin/bash

# Script para fazer backup dos diretórios importantes
# Execute este script no servidor original antes de usar o Dockerfile
# Gerado automaticamente em Tue Sep 16 17:03:45 -03 2025

BACKUP_DIR="./backup"
mkdir -p "$BACKUP_DIR"

echo "=== Iniciando backup dos diretórios descobertos ==="
echo "Data/Hora: $(date)"
echo ""

# Diretórios descobertos automaticamente:
if [ -d "/etc" ]; then
    echo "Fazendo backup de /etc..."
    mkdir -p "$BACKUP_DIR/etc"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/etc/" "$BACKUP_DIR/etc/" 2>/dev/null || true
    echo "  Backup de /etc concluído"
fi

if [ -d "/home/<USER>" ]; then
    echo "Fazendo backup de /home/<USER>"
    mkdir -p "$BACKUP_DIR/home/<USER>"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/home/<USER>/" "$BACKUP_DIR/home/<USER>/" 2>/dev/null || true
    echo "  Backup de /home/<USER>"
fi

if [ -d "/home/<USER>/pnet-clone" ]; then
    echo "Fazendo backup de /home/<USER>/pnet-clone..."
    mkdir -p "$BACKUP_DIR/home/<USER>/pnet-clone"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/home/<USER>/pnet-clone/" "$BACKUP_DIR/home/<USER>/pnet-clone/" 2>/dev/null || true
    echo "  Backup de /home/<USER>/pnet-clone concluído"
fi

if [ -d "/srv" ]; then
    echo "Fazendo backup de /srv..."
    mkdir -p "$BACKUP_DIR/srv"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/srv/" "$BACKUP_DIR/srv/" 2>/dev/null || true
    echo "  Backup de /srv concluído"
fi

if [ -d "/srv/flow-collect" ]; then
    echo "Fazendo backup de /srv/flow-collect..."
    mkdir -p "$BACKUP_DIR/srv/flow-collect"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/srv/flow-collect/" "$BACKUP_DIR/srv/flow-collect/" 2>/dev/null || true
    echo "  Backup de /srv/flow-collect concluído"
fi

if [ -d "/srv/flow-collect/include" ]; then
    echo "Fazendo backup de /srv/flow-collect/include..."
    mkdir -p "$BACKUP_DIR/srv/flow-collect/include"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/srv/flow-collect/include/" "$BACKUP_DIR/srv/flow-collect/include/" 2>/dev/null || true
    echo "  Backup de /srv/flow-collect/include concluído"
fi

if [ -d "/usr/local" ]; then
    echo "Fazendo backup de /usr/local..."
    mkdir -p "$BACKUP_DIR/usr/local"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/local/" "$BACKUP_DIR/usr/local/" 2>/dev/null || true
    echo "  Backup de /usr/local concluído"
fi

if [ -d "/usr/share" ]; then
    echo "Fazendo backup de /usr/share..."
    mkdir -p "$BACKUP_DIR/usr/share"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/" "$BACKUP_DIR/usr/share/" 2>/dev/null || true
    echo "  Backup de /usr/share concluído"
fi

if [ -d "/usr/share/Modules" ]; then
    echo "Fazendo backup de /usr/share/Modules..."
    mkdir -p "$BACKUP_DIR/usr/share/Modules"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/Modules/" "$BACKUP_DIR/usr/share/Modules/" 2>/dev/null || true
    echo "  Backup de /usr/share/Modules concluído"
fi

if [ -d "/usr/share/Modules/bin" ]; then
    echo "Fazendo backup de /usr/share/Modules/bin..."
    mkdir -p "$BACKUP_DIR/usr/share/Modules/bin"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/Modules/bin/" "$BACKUP_DIR/usr/share/Modules/bin/" 2>/dev/null || true
    echo "  Backup de /usr/share/Modules/bin concluído"
fi

if [ -d "/usr/share/Modules/init" ]; then
    echo "Fazendo backup de /usr/share/Modules/init..."
    mkdir -p "$BACKUP_DIR/usr/share/Modules/init"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/Modules/init/" "$BACKUP_DIR/usr/share/Modules/init/" 2>/dev/null || true
    echo "  Backup de /usr/share/Modules/init concluído"
fi

if [ -d "/usr/share/automake-1.13" ]; then
    echo "Fazendo backup de /usr/share/automake-1.13..."
    mkdir -p "$BACKUP_DIR/usr/share/automake-1.13"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/automake-1.13/" "$BACKUP_DIR/usr/share/automake-1.13/" 2>/dev/null || true
    echo "  Backup de /usr/share/automake-1.13 concluído"
fi

if [ -d "/usr/share/gcc-4.8.2/python" ]; then
    echo "Fazendo backup de /usr/share/gcc-4.8.2/python..."
    mkdir -p "$BACKUP_DIR/usr/share/gcc-4.8.2/python"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/gcc-4.8.2/python/" "$BACKUP_DIR/usr/share/gcc-4.8.2/python/" 2>/dev/null || true
    echo "  Backup de /usr/share/gcc-4.8.2/python concluído"
fi

if [ -d "/usr/share/gcc-4.8.2/python/libstdcxx" ]; then
    echo "Fazendo backup de /usr/share/gcc-4.8.2/python/libstdcxx..."
    mkdir -p "$BACKUP_DIR/usr/share/gcc-4.8.2/python/libstdcxx"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/gcc-4.8.2/python/libstdcxx/" "$BACKUP_DIR/usr/share/gcc-4.8.2/python/libstdcxx/" 2>/dev/null || true
    echo "  Backup de /usr/share/gcc-4.8.2/python/libstdcxx concluído"
fi

if [ -d "/usr/share/gdb/auto-load" ]; then
    echo "Fazendo backup de /usr/share/gdb/auto-load..."
    mkdir -p "$BACKUP_DIR/usr/share/gdb/auto-load"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/gdb/auto-load/" "$BACKUP_DIR/usr/share/gdb/auto-load/" 2>/dev/null || true
    echo "  Backup de /usr/share/gdb/auto-load concluído"
fi

if [ -d "/usr/share/gdb/auto-load/usr" ]; then
    echo "Fazendo backup de /usr/share/gdb/auto-load/usr..."
    mkdir -p "$BACKUP_DIR/usr/share/gdb/auto-load/usr"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/gdb/auto-load/usr/" "$BACKUP_DIR/usr/share/gdb/auto-load/usr/" 2>/dev/null || true
    echo "  Backup de /usr/share/gdb/auto-load/usr concluído"
fi

if [ -d "/usr/share/gdb/python" ]; then
    echo "Fazendo backup de /usr/share/gdb/python..."
    mkdir -p "$BACKUP_DIR/usr/share/gdb/python"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/gdb/python/" "$BACKUP_DIR/usr/share/gdb/python/" 2>/dev/null || true
    echo "  Backup de /usr/share/gdb/python concluído"
fi

if [ -d "/usr/share/gdb/python/gdb" ]; then
    echo "Fazendo backup de /usr/share/gdb/python/gdb..."
    mkdir -p "$BACKUP_DIR/usr/share/gdb/python/gdb"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/gdb/python/gdb/" "$BACKUP_DIR/usr/share/gdb/python/gdb/" 2>/dev/null || true
    echo "  Backup de /usr/share/gdb/python/gdb concluído"
fi

if [ -d "/usr/share/git-core" ]; then
    echo "Fazendo backup de /usr/share/git-core..."
    mkdir -p "$BACKUP_DIR/usr/share/git-core"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/git-core/" "$BACKUP_DIR/usr/share/git-core/" 2>/dev/null || true
    echo "  Backup de /usr/share/git-core concluído"
fi

if [ -d "/usr/share/git-core/contrib" ]; then
    echo "Fazendo backup de /usr/share/git-core/contrib..."
    mkdir -p "$BACKUP_DIR/usr/share/git-core/contrib"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/git-core/contrib/" "$BACKUP_DIR/usr/share/git-core/contrib/" 2>/dev/null || true
    echo "  Backup de /usr/share/git-core/contrib concluído"
fi

if [ -d "/usr/share/git-core/contrib/completion" ]; then
    echo "Fazendo backup de /usr/share/git-core/contrib/completion..."
    mkdir -p "$BACKUP_DIR/usr/share/git-core/contrib/completion"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/git-core/contrib/completion/" "$BACKUP_DIR/usr/share/git-core/contrib/completion/" 2>/dev/null || true
    echo "  Backup de /usr/share/git-core/contrib/completion concluído"
fi

if [ -d "/usr/share/httpd" ]; then
    echo "Fazendo backup de /usr/share/httpd..."
    mkdir -p "$BACKUP_DIR/usr/share/httpd"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/httpd/" "$BACKUP_DIR/usr/share/httpd/" 2>/dev/null || true
    echo "  Backup de /usr/share/httpd concluído"
fi

if [ -d "/usr/share/httpd/icons" ]; then
    echo "Fazendo backup de /usr/share/httpd/icons..."
    mkdir -p "$BACKUP_DIR/usr/share/httpd/icons"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/httpd/icons/" "$BACKUP_DIR/usr/share/httpd/icons/" 2>/dev/null || true
    echo "  Backup de /usr/share/httpd/icons concluído"
fi

if [ -d "/usr/share/httpd/icons/small" ]; then
    echo "Fazendo backup de /usr/share/httpd/icons/small..."
    mkdir -p "$BACKUP_DIR/usr/share/httpd/icons/small"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/httpd/icons/small/" "$BACKUP_DIR/usr/share/httpd/icons/small/" 2>/dev/null || true
    echo "  Backup de /usr/share/httpd/icons/small concluído"
fi

if [ -d "/usr/share/httpd/noindex" ]; then
    echo "Fazendo backup de /usr/share/httpd/noindex..."
    mkdir -p "$BACKUP_DIR/usr/share/httpd/noindex"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/httpd/noindex/" "$BACKUP_DIR/usr/share/httpd/noindex/" 2>/dev/null || true
    echo "  Backup de /usr/share/httpd/noindex concluído"
fi

if [ -d "/usr/share/libtool" ]; then
    echo "Fazendo backup de /usr/share/libtool..."
    mkdir -p "$BACKUP_DIR/usr/share/libtool"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/libtool/" "$BACKUP_DIR/usr/share/libtool/" 2>/dev/null || true
    echo "  Backup de /usr/share/libtool concluído"
fi

if [ -d "/usr/share/libtool/config" ]; then
    echo "Fazendo backup de /usr/share/libtool/config..."
    mkdir -p "$BACKUP_DIR/usr/share/libtool/config"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/libtool/config/" "$BACKUP_DIR/usr/share/libtool/config/" 2>/dev/null || true
    echo "  Backup de /usr/share/libtool/config concluído"
fi

if [ -d "/usr/share/pear" ]; then
    echo "Fazendo backup de /usr/share/pear..."
    mkdir -p "$BACKUP_DIR/usr/share/pear"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/" "$BACKUP_DIR/usr/share/pear/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear concluído"
fi

if [ -d "/usr/share/pear/Archive" ]; then
    echo "Fazendo backup de /usr/share/pear/Archive..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/Archive"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/Archive/" "$BACKUP_DIR/usr/share/pear/Archive/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/Archive concluído"
fi

if [ -d "/usr/share/pear/Console" ]; then
    echo "Fazendo backup de /usr/share/pear/Console..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/Console"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/Console/" "$BACKUP_DIR/usr/share/pear/Console/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/Console concluído"
fi

if [ -d "/usr/share/pear/OS" ]; then
    echo "Fazendo backup de /usr/share/pear/OS..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/OS"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/OS/" "$BACKUP_DIR/usr/share/pear/OS/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/OS concluído"
fi

if [ -d "/usr/share/pear/PEAR" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/" "$BACKUP_DIR/usr/share/pear/PEAR/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR concluído"
fi

if [ -d "/usr/share/pear/PEAR/ChannelFile" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/ChannelFile..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/ChannelFile"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/ChannelFile/" "$BACKUP_DIR/usr/share/pear/PEAR/ChannelFile/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/ChannelFile concluído"
fi

if [ -d "/usr/share/pear/PEAR/Command" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/Command..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/Command"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/Command/" "$BACKUP_DIR/usr/share/pear/PEAR/Command/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/Command concluído"
fi

if [ -d "/usr/share/pear/PEAR/Downloader" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/Downloader..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/Downloader"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/Downloader/" "$BACKUP_DIR/usr/share/pear/PEAR/Downloader/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/Downloader concluído"
fi

if [ -d "/usr/share/pear/PEAR/Frontend" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/Frontend..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/Frontend"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/Frontend/" "$BACKUP_DIR/usr/share/pear/PEAR/Frontend/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/Frontend concluído"
fi

if [ -d "/usr/share/pear/PEAR/Installer" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/Installer..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/Installer"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/Installer/" "$BACKUP_DIR/usr/share/pear/PEAR/Installer/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/Installer concluído"
fi

if [ -d "/usr/share/pear/PEAR/PackageFile" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/PackageFile..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/PackageFile"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/PackageFile/" "$BACKUP_DIR/usr/share/pear/PEAR/PackageFile/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/PackageFile concluído"
fi

if [ -d "/usr/share/pear/PEAR/REST" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/REST..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/REST"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/REST/" "$BACKUP_DIR/usr/share/pear/PEAR/REST/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/REST concluído"
fi

if [ -d "/usr/share/pear/PEAR/Task" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/Task..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/Task"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/Task/" "$BACKUP_DIR/usr/share/pear/PEAR/Task/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/Task concluído"
fi

if [ -d "/usr/share/pear/PEAR/Validator" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR/Validator..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR/Validator"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR/Validator/" "$BACKUP_DIR/usr/share/pear/PEAR/Validator/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR/Validator concluído"
fi

if [ -d "/usr/share/pear/PEAR2" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR2..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR2"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR2/" "$BACKUP_DIR/usr/share/pear/PEAR2/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR2 concluído"
fi

if [ -d "/usr/share/pear/PEAR2/Cache" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR2/Cache..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR2/Cache"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR2/Cache/" "$BACKUP_DIR/usr/share/pear/PEAR2/Cache/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR2/Cache concluído"
fi

if [ -d "/usr/share/pear/PEAR2/Console" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR2/Console..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR2/Console"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR2/Console/" "$BACKUP_DIR/usr/share/pear/PEAR2/Console/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR2/Console concluído"
fi

if [ -d "/usr/share/pear/PEAR2/Net" ]; then
    echo "Fazendo backup de /usr/share/pear/PEAR2/Net..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/PEAR2/Net"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/PEAR2/Net/" "$BACKUP_DIR/usr/share/pear/PEAR2/Net/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/PEAR2/Net concluído"
fi

if [ -d "/usr/share/pear/Structures" ]; then
    echo "Fazendo backup de /usr/share/pear/Structures..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/Structures"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/Structures/" "$BACKUP_DIR/usr/share/pear/Structures/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/Structures concluído"
fi

if [ -d "/usr/share/pear/Structures/Graph" ]; then
    echo "Fazendo backup de /usr/share/pear/Structures/Graph..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/Structures/Graph"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/Structures/Graph/" "$BACKUP_DIR/usr/share/pear/Structures/Graph/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/Structures/Graph concluído"
fi

if [ -d "/usr/share/pear/XML" ]; then
    echo "Fazendo backup de /usr/share/pear/XML..."
    mkdir -p "$BACKUP_DIR/usr/share/pear/XML"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/pear/XML/" "$BACKUP_DIR/usr/share/pear/XML/" 2>/dev/null || true
    echo "  Backup de /usr/share/pear/XML concluído"
fi

if [ -d "/usr/share/perl5" ]; then
    echo "Fazendo backup de /usr/share/perl5..."
    mkdir -p "$BACKUP_DIR/usr/share/perl5"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/perl5/" "$BACKUP_DIR/usr/share/perl5/" 2>/dev/null || true
    echo "  Backup de /usr/share/perl5 concluído"
fi

if [ -d "/usr/share/perl5/Unicode/Collate" ]; then
    echo "Fazendo backup de /usr/share/perl5/Unicode/Collate..."
    mkdir -p "$BACKUP_DIR/usr/share/perl5/Unicode/Collate"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/perl5/Unicode/Collate/" "$BACKUP_DIR/usr/share/perl5/Unicode/Collate/" 2>/dev/null || true
    echo "  Backup de /usr/share/perl5/Unicode/Collate concluído"
fi

if [ -d "/usr/share/perl5/unicore" ]; then
    echo "Fazendo backup de /usr/share/perl5/unicore..."
    mkdir -p "$BACKUP_DIR/usr/share/perl5/unicore"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/perl5/unicore/" "$BACKUP_DIR/usr/share/perl5/unicore/" 2>/dev/null || true
    echo "  Backup de /usr/share/perl5/unicore concluído"
fi

if [ -d "/usr/share/perl5/unicore/To" ]; then
    echo "Fazendo backup de /usr/share/perl5/unicore/To..."
    mkdir -p "$BACKUP_DIR/usr/share/perl5/unicore/To"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/perl5/unicore/To/" "$BACKUP_DIR/usr/share/perl5/unicore/To/" 2>/dev/null || true
    echo "  Backup de /usr/share/perl5/unicore/To concluído"
fi

if [ -d "/usr/share/perl5/unicore/lib" ]; then
    echo "Fazendo backup de /usr/share/perl5/unicore/lib..."
    mkdir -p "$BACKUP_DIR/usr/share/perl5/unicore/lib"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/perl5/unicore/lib/" "$BACKUP_DIR/usr/share/perl5/unicore/lib/" 2>/dev/null || true
    echo "  Backup de /usr/share/perl5/unicore/lib concluído"
fi

if [ -d "/usr/share/perl5/vendor_perl" ]; then
    echo "Fazendo backup de /usr/share/perl5/vendor_perl..."
    mkdir -p "$BACKUP_DIR/usr/share/perl5/vendor_perl"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/perl5/vendor_perl/" "$BACKUP_DIR/usr/share/perl5/vendor_perl/" 2>/dev/null || true
    echo "  Backup de /usr/share/perl5/vendor_perl concluído"
fi

if [ -d "/usr/share/perl5/vendor_perl/Business" ]; then
    echo "Fazendo backup de /usr/share/perl5/vendor_perl/Business..."
    mkdir -p "$BACKUP_DIR/usr/share/perl5/vendor_perl/Business"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/perl5/vendor_perl/Business/" "$BACKUP_DIR/usr/share/perl5/vendor_perl/Business/" 2>/dev/null || true
    echo "  Backup de /usr/share/perl5/vendor_perl/Business concluído"
fi

if [ -d "/usr/share/php/Fedora" ]; then
    echo "Fazendo backup de /usr/share/php/Fedora..."
    mkdir -p "$BACKUP_DIR/usr/share/php/Fedora"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/php/Fedora/" "$BACKUP_DIR/usr/share/php/Fedora/" 2>/dev/null || true
    echo "  Backup de /usr/share/php/Fedora concluído"
fi

if [ -d "/usr/share/php/Fedora/Autoloader" ]; then
    echo "Fazendo backup de /usr/share/php/Fedora/Autoloader..."
    mkdir -p "$BACKUP_DIR/usr/share/php/Fedora/Autoloader"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/php/Fedora/Autoloader/" "$BACKUP_DIR/usr/share/php/Fedora/Autoloader/" 2>/dev/null || true
    echo "  Backup de /usr/share/php/Fedora/Autoloader concluído"
fi

if [ -d "/usr/share/php/Seld" ]; then
    echo "Fazendo backup de /usr/share/php/Seld..."
    mkdir -p "$BACKUP_DIR/usr/share/php/Seld"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/php/Seld/" "$BACKUP_DIR/usr/share/php/Seld/" 2>/dev/null || true
    echo "  Backup de /usr/share/php/Seld concluído"
fi

if [ -d "/usr/share/php/Seld/JsonLint" ]; then
    echo "Fazendo backup de /usr/share/php/Seld/JsonLint..."
    mkdir -p "$BACKUP_DIR/usr/share/php/Seld/JsonLint"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/php/Seld/JsonLint/" "$BACKUP_DIR/usr/share/php/Seld/JsonLint/" 2>/dev/null || true
    echo "  Backup de /usr/share/php/Seld/JsonLint concluído"
fi

if [ -d "/usr/share/swig/2.0.10" ]; then
    echo "Fazendo backup de /usr/share/swig/2.0.10..."
    mkdir -p "$BACKUP_DIR/usr/share/swig/2.0.10"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/swig/2.0.10/" "$BACKUP_DIR/usr/share/swig/2.0.10/" 2>/dev/null || true
    echo "  Backup de /usr/share/swig/2.0.10 concluído"
fi

if [ -d "/usr/share/swig/2.0.10/perl5" ]; then
    echo "Fazendo backup de /usr/share/swig/2.0.10/perl5..."
    mkdir -p "$BACKUP_DIR/usr/share/swig/2.0.10/perl5"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/swig/2.0.10/perl5/" "$BACKUP_DIR/usr/share/swig/2.0.10/perl5/" 2>/dev/null || true
    echo "  Backup de /usr/share/swig/2.0.10/perl5 concluído"
fi

if [ -d "/usr/share/swig/2.0.10/ruby" ]; then
    echo "Fazendo backup de /usr/share/swig/2.0.10/ruby..."
    mkdir -p "$BACKUP_DIR/usr/share/swig/2.0.10/ruby"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/swig/2.0.10/ruby/" "$BACKUP_DIR/usr/share/swig/2.0.10/ruby/" 2>/dev/null || true
    echo "  Backup de /usr/share/swig/2.0.10/ruby concluído"
fi

if [ -d "/usr/share/tests/pear/Structures_Graph" ]; then
    echo "Fazendo backup de /usr/share/tests/pear/Structures_Graph..."
    mkdir -p "$BACKUP_DIR/usr/share/tests/pear/Structures_Graph"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/tests/pear/Structures_Graph/" "$BACKUP_DIR/usr/share/tests/pear/Structures_Graph/" 2>/dev/null || true
    echo "  Backup de /usr/share/tests/pear/Structures_Graph concluído"
fi

if [ -d "/usr/share/tests/pear/XML_Util" ]; then
    echo "Fazendo backup de /usr/share/tests/pear/XML_Util..."
    mkdir -p "$BACKUP_DIR/usr/share/tests/pear/XML_Util"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/tests/pear/XML_Util/" "$BACKUP_DIR/usr/share/tests/pear/XML_Util/" 2>/dev/null || true
    echo "  Backup de /usr/share/tests/pear/XML_Util concluído"
fi

if [ -d "/usr/share/vim/vim74" ]; then
    echo "Fazendo backup de /usr/share/vim/vim74..."
    mkdir -p "$BACKUP_DIR/usr/share/vim/vim74"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/vim/vim74/" "$BACKUP_DIR/usr/share/vim/vim74/" 2>/dev/null || true
    echo "  Backup de /usr/share/vim/vim74 concluído"
fi

if [ -d "/usr/share/vim/vim74/doc" ]; then
    echo "Fazendo backup de /usr/share/vim/vim74/doc..."
    mkdir -p "$BACKUP_DIR/usr/share/vim/vim74/doc"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/vim/vim74/doc/" "$BACKUP_DIR/usr/share/vim/vim74/doc/" 2>/dev/null || true
    echo "  Backup de /usr/share/vim/vim74/doc concluído"
fi

if [ -d "/usr/share/vim/vim74/macros" ]; then
    echo "Fazendo backup de /usr/share/vim/vim74/macros..."
    mkdir -p "$BACKUP_DIR/usr/share/vim/vim74/macros"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/vim/vim74/macros/" "$BACKUP_DIR/usr/share/vim/vim74/macros/" 2>/dev/null || true
    echo "  Backup de /usr/share/vim/vim74/macros concluído"
fi

if [ -d "/usr/share/vim/vim74/tutor" ]; then
    echo "Fazendo backup de /usr/share/vim/vim74/tutor..."
    mkdir -p "$BACKUP_DIR/usr/share/vim/vim74/tutor"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/vim/vim74/tutor/" "$BACKUP_DIR/usr/share/vim/vim74/tutor/" 2>/dev/null || true
    echo "  Backup de /usr/share/vim/vim74/tutor concluído"
fi

if [ -d "/usr/share/yum-cli" ]; then
    echo "Fazendo backup de /usr/share/yum-cli..."
    mkdir -p "$BACKUP_DIR/usr/share/yum-cli"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \
          --exclude='sys' --exclude='dev' --exclude='run' \
          "/usr/share/yum-cli/" "$BACKUP_DIR/usr/share/yum-cli/" 2>/dev/null || true
    echo "  Backup de /usr/share/yum-cli concluído"
fi

echo ""
echo "=== Backup completo ==="
echo "Diretório de backup: $BACKUP_DIR"
echo "Tamanho total: $(du -sh $BACKUP_DIR 2>/dev/null | cut -f1)"
echo "Arquivos copiados: $(find $BACKUP_DIR -type f | wc -l)"
echo ""
echo "Para usar com Docker:"
echo "1. docker build -t $CONTAINER_NAME ."
echo "2. docker run -d --name $CONTAINER_NAME $CONTAINER_NAME"
